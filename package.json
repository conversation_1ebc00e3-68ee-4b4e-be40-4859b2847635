{"name": "colonfit", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@paypal/react-paypal-js": "^8.7.0", "@react-email/components": "^0.0.25", "@reduxjs/toolkit": "^2.2.7", "@stripe/react-stripe-js": "^2.8.0", "@stripe/stripe-js": "^4.5.0", "@types/react-redux": "^7.1.34", "axios": "^1.7.7", "cors": "^2.8.5", "countries-list": "^3.1.1", "framer-motion": "^12.12.2", "klaviyo-api": "^14.0.0", "lucide-react": "^0.445.0", "next": "14.2.11", "react": "^18", "react-dom": "^18", "react-facebook-pixel": "^1.0.4", "react-hook-form": "^7.53.0", "react-redux": "^9.1.2", "recharts": "^2.12.7", "redux": "^5.0.1", "redux-persist": "^6.0.0", "resend": "^4.0.0", "sharp": "^0.33.5", "stripe": "^16.12.0"}, "devDependencies": {"@types/node": "^20.16.10", "@types/react": "^18", "@types/react-dom": "^18", "eslint": "^8", "eslint-config-next": "14.2.11", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}