import { useState, useEffect } from 'react';

function useLocalStorage<T>(key: string, initialValue: T): [T, (value: T) => void] {
    const [storedValue, setStoredValue] = useState<T>(initialValue);

    useEffect(() => {
        
        const item = window.localStorage.getItem(key);
       
        if (item !== null) {
            try {
                setStoredValue(JSON.parse(item));
            } catch {
                setStoredValue(item as T);
            }
        }
    }, [key, initialValue]);

    const setValue = (value: T) => {
       
        setStoredValue(value);
       
        if (typeof value === 'string') {
            window.localStorage.setItem(key, value);
        } else {
            window.localStorage.setItem(key, JSON.stringify(value));
        }
    };

    return [storedValue, setValue];
}

export default useLocalStorage;