import { useState, useEffect } from 'react';

const useOfferTimer = () => {
    const [timeLeft, setTimeLeft] = useState(300); 

    useEffect(() => {
        const startTime = localStorage.getItem('offerStartTime');

        if (!startTime) {
            localStorage.setItem('offerStartTime', Date.now().toString());
            setTimeLeft(300);
        } else {
            const elapsed = Math.floor((Date.now() - parseInt(startTime)) / 1000);
            const remaining = Math.max(300 - elapsed, 0);
            setTimeLeft(remaining);
        }
    }, []);

    useEffect(() => {
        if (timeLeft === 0) {
            localStorage.setItem('offerStartTime', Date.now().toString());
            setTimeLeft(300);
        }

        const timer = setInterval(() => {
            setTimeLeft((prev) => Math.max(prev - 1, 0));
        }, 1000);

        return () => clearInterval(timer);
    }, [timeLeft]);

    const formatTime = (seconds: number): string => {
        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = seconds % 60;
        return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
    };

    return { timeLeft, formatTime };
};

export default useOfferTimer;