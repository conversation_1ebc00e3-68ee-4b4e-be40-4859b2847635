"use client";

import { useCallback, useEffect } from "react";
import {
  logPixelEvent,
  logPixelSuccess,
  logPixelError,
} from "../utils/pixelLogger";

type FbqFunction = (
  method: string,
  eventName: string,
  params?: Record<string, unknown>
) => void;

interface FbqObject {
  q?: unknown[];
  getState?: () => {
    pixels?: Array<{
      id: string;
      [key: string]: any;
    }>;
  };
}

type Fbq = FbqFunction & FbqObject;

declare global {
  interface Window {
    fbq: Fbq;
  }
}

const useFacebookPixel = () => {
  useEffect(() => {
    if (typeof window !== "undefined") {
      // Check if fbq is already initialized with our pixel
      const isPixelAlreadyInitialized =
        window.fbq &&
        typeof window.fbq.getState === "function" &&
        window.fbq.getState?.()?.pixels &&
        window.fbq
          .getState?.()
          ?.pixels?.some(
            (pixel: any) =>
              pixel.id === process.env.NEXT_PUBLIC_FACEBOOK_PIXEL_ID
          );

      // Only initialize if not already initialized
      if (!isPixelAlreadyInitialized) {
        window.fbq =
          window.fbq ||
          (function (this: Fbq, method, eventName, params) {
            (this.q = this.q || []).push([method, eventName, params]);
          } as Fbq);

        if (process.env.NEXT_PUBLIC_FACEBOOK_PIXEL_ID) {
          console.log(
            "🎯 Initializing Facebook Pixel with ID:",
            process.env.NEXT_PUBLIC_FACEBOOK_PIXEL_ID
          );
          window.fbq("init", process.env.NEXT_PUBLIC_FACEBOOK_PIXEL_ID);
          window.fbq("track", "PageView");
          console.log(
            "📄 PageView tracked for pixel:",
            process.env.NEXT_PUBLIC_FACEBOOK_PIXEL_ID
          );
        }

        // Enhanced fbclid handling with multiple storage methods
        const urlParams = new URLSearchParams(window.location.search);
        const fbclid = urlParams.get("fbclid");

        if (fbclid) {
          // Store in multiple places for persistence
          if (!sessionStorage.getItem("fbclid")) {
            sessionStorage.setItem("fbclid", fbclid);
          }
          if (!localStorage.getItem("fbclid")) {
            localStorage.setItem("fbclid", fbclid);
          }
          console.log("🔗 Facebook click ID stored:", fbclid);
        }
      }
    }
  }, []);

  const checkPixelCookies = () => {
    if (typeof window !== "undefined") {
      const cookies = document.cookie.split(";");
      const fbpCookie = cookies.find((cookie) =>
        cookie.trim().startsWith("_fbp=")
      );
      const fbcCookie = cookies.find((cookie) =>
        cookie.trim().startsWith("_fbc=")
      );
      const fbclidCookie = cookies.find((cookie) =>
        cookie.trim().startsWith("fbclid=")
      );

      // Enhanced fbclid retrieval with multiple fallbacks
      let fbclid = null;

      // 1. Check URL parameters first
      if (window.location.search) {
        const urlParams = new URLSearchParams(window.location.search);
        fbclid = urlParams.get("fbclid");
      }

      // 2. If not in URL, try sessionStorage
      if (!fbclid) {
        fbclid = sessionStorage.getItem("fbclid");
      }

      // 3. If not in sessionStorage, try localStorage
      if (!fbclid) {
        fbclid = localStorage.getItem("fbclid");
      }

      // 4. If not in localStorage, try cookie
      if (!fbclid && fbclidCookie) {
        fbclid = fbclidCookie.split("=")[1];
      }

      const cookieData = {
        fbp: fbpCookie ? fbpCookie.split("=")[1] : null,
        fbc: fbcCookie ? fbcCookie.split("=")[1] : null,
        fbclid: fbclid,
      };

      console.log("🍪 Pixel Cookies Check:", cookieData);

      if (fbclid) {
        console.log("🔗 Retrieved Facebook click ID for tracking:", fbclid);
      }

      return cookieData;
    }
    return { fbp: null, fbc: null, fbclid: null };
  };

  const getPixelId = () => {
    return process.env.NEXT_PUBLIC_FACEBOOK_PIXEL_ID;
  };

  const trackPageView = useCallback(() => {
    if (typeof window !== "undefined" && window.fbq) {
      window.fbq("track", "PageView");
      console.log("📄 PageView tracked");
      checkPixelCookies();
    }
  }, []);

  const trackInitiateCheckout = useCallback(
    async (
      contentIds: string[],
      contentType: string,
      email: string,
      value?: number,
      currency?: string,
      additionalUserData?: {
        firstName?: string;
        lastName?: string;
        phone?: string;
        city?: string;
        state?: string;
        country?: string;
        zip?: string;
        dateOfBirth?: string;
        externalId?: string;
      }
    ) => {
      try {
        const cookies = checkPixelCookies();
        const pixelId = getPixelId();

        // Log event initiation
        logPixelEvent("InitiateCheckout", {
          source: "frontend",
          pixelId,
          email,
          value: value || 0,
          currency: currency || "USD",
          contentIds,
          fbclid: cookies.fbclid || undefined,
          fbp: cookies.fbp || undefined,
          fbc: cookies.fbc || undefined,
        });

        // Enhanced frontend logging
        console.log(
          "🛒 [FRONTEND] Tracking InitiateCheckout with configuration:",
          {
            pixelId,
            email: email ? "[REDACTED]" : "none",
            contentIds,
            value: value || 0,
            currency: currency || "USD",
            fbclid: cookies.fbclid,
            fbp: cookies.fbp ? "[PRESENT]" : "none",
            fbc: cookies.fbc ? "[PRESENT]" : "none",
            timestamp: new Date().toISOString(),
          }
        );

        // Also track on frontend pixel for immediate feedback
        if (window.fbq) {
          window.fbq("track", "InitiateCheckout", {
            content_ids: contentIds,
            content_type: contentType,
            value: value || 0,
            currency: currency || "USD",
          });
          console.log(
            "🎯 [FRONTEND] InitiateCheckout tracked via browser pixel"
          );
        }

        const response = await fetch(
          "/api/meta-pixel/track-initiate-checkout",
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              data: {
                contentIds,
                contentType,
                fbp: cookies.fbp,
                fbc: cookies.fbc,
                fbclid: cookies.fbclid,
                email,
                value: value || 0,
                currency: currency || "USD",
                pixelId,
                // Include additional user data if available
                ...(additionalUserData?.firstName && {
                  firstName: additionalUserData.firstName,
                }),
                ...(additionalUserData?.lastName && {
                  lastName: additionalUserData.lastName,
                }),
                ...(additionalUserData?.phone && {
                  phone: additionalUserData.phone,
                }),
                ...(additionalUserData?.city && {
                  city: additionalUserData.city,
                }),
                ...(additionalUserData?.state && {
                  state: additionalUserData.state,
                }),
                ...(additionalUserData?.country && {
                  country: additionalUserData.country,
                }),
                ...(additionalUserData?.zip && { zip: additionalUserData.zip }),
                ...(additionalUserData?.dateOfBirth && {
                  dateOfBirth: additionalUserData.dateOfBirth,
                }),
                ...(additionalUserData?.externalId && {
                  externalId: additionalUserData.externalId,
                }),
              },
            }),
          }
        );

        if (!response.ok) {
          throw new Error(
            `HTTP ${response.status}: Failed to track InitiateCheckout event`
          );
        }

        const result = await response.json();
        console.log(
          "✅ [BACKEND] InitiateCheckout event tracked successfully:",
          result
        );

        // Log success
        logPixelSuccess("InitiateCheckout", {
          source: "backend",
          pixelId,
          email,
          metaResponse: result,
        });
      } catch (error) {
        console.error(
          "❌ [ERROR] Error tracking InitiateCheckout event:",
          error
        );

        // Log error
        logPixelError(
          "InitiateCheckout",
          error instanceof Error ? error.message : "Unknown error",
          {
            source: "backend",
            pixelId: getPixelId(),
            email,
          }
        );

        // Don't throw error to prevent breaking user flow
      }
    },
    []
  );

  const trackCustomEvent = (
    event: string,
    data: Record<string, unknown> = {}
  ) => {
    if (typeof window !== "undefined" && window.fbq) {
      window.fbq("track", event, data);
      console.log(`🎯 [FRONTEND] Custom event tracked: ${event}`, data);
    }
  };

  const trackPurchase = useCallback(
    async (
      value: number,
      currency: string,
      contentIds: string[],
      contentType: string,
      email: string,
      additionalUserData?: {
        firstName?: string;
        lastName?: string;
        phone?: string;
        city?: string;
        state?: string;
        country?: string;
        zip?: string;
        dateOfBirth?: string;
        externalId?: string;
      }
    ) => {
      try {
        const cookies = checkPixelCookies();
        const pixelId = getPixelId();

        // Log event initiation
        logPixelEvent("Purchase", {
          source: "frontend",
          pixelId,
          email,
          value,
          currency,
          contentIds,
          fbclid: cookies.fbclid || undefined,
          fbp: cookies.fbp || undefined,
          fbc: cookies.fbc || undefined,
        });

        // Enhanced frontend logging
        console.log("💰 [FRONTEND] Tracking Purchase with configuration:", {
          pixelId,
          email: email ? "[REDACTED]" : "none",
          value,
          currency,
          contentIds,
          fbclid: cookies.fbclid,
          fbp: cookies.fbp ? "[PRESENT]" : "none",
          fbc: cookies.fbc ? "[PRESENT]" : "none",
          timestamp: new Date().toISOString(),
        });

        // Also track on frontend pixel for immediate feedback
        if (window.fbq) {
          window.fbq("track", "Purchase", {
            value: value,
            currency: currency,
            content_ids: contentIds,
            content_type: contentType,
          });
          console.log("🎯 [FRONTEND] Purchase tracked via browser pixel");
        }

        const response = await fetch("/api/meta-pixel/track-purchase", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            value,
            currency,
            contentIds,
            contentType,
            fbp: cookies.fbp,
            fbc: cookies.fbc,
            fbclid: cookies.fbclid,
            email,
            pixelId,
            // Include additional user data if available
            ...(additionalUserData?.firstName && {
              firstName: additionalUserData.firstName,
            }),
            ...(additionalUserData?.lastName && {
              lastName: additionalUserData.lastName,
            }),
            ...(additionalUserData?.phone && {
              phone: additionalUserData.phone,
            }),
            ...(additionalUserData?.city && { city: additionalUserData.city }),
            ...(additionalUserData?.state && {
              state: additionalUserData.state,
            }),
            ...(additionalUserData?.country && {
              country: additionalUserData.country,
            }),
            ...(additionalUserData?.zip && { zip: additionalUserData.zip }),
            ...(additionalUserData?.dateOfBirth && {
              dateOfBirth: additionalUserData.dateOfBirth,
            }),
            ...(additionalUserData?.externalId && {
              externalId: additionalUserData.externalId,
            }),
          }),
        });

        if (!response.ok) {
          throw new Error(
            `HTTP ${response.status}: Failed to track Purchase event`
          );
        }

        const result = await response.json();
        console.log(
          "✅ [BACKEND] Purchase event tracked successfully:",
          result
        );

        // Log success
        logPixelSuccess("Purchase", {
          source: "backend",
          pixelId,
          email,
          metaResponse: result,
        });
      } catch (error) {
        console.error("❌ [ERROR] Error tracking Purchase event:", error);

        // Log error
        logPixelError(
          "Purchase",
          error instanceof Error ? error.message : "Unknown error",
          {
            source: "backend",
            pixelId: getPixelId(),
            email,
          }
        );

        // Don't throw error to prevent breaking user flow
      }
    },
    []
  );

  const trackAddToCart = useCallback(
    async (
      email: string,
      contentIds?: string[],
      contentType?: string,
      value?: number,
      currency?: string,
      additionalUserData?: {
        firstName?: string;
        lastName?: string;
        phone?: string;
        city?: string;
        state?: string;
        country?: string;
        zip?: string;
        dateOfBirth?: string;
        externalId?: string;
      }
    ) => {
      try {
        const cookies = checkPixelCookies();
        const pixelId = getPixelId();

        const trackingData = {
          contentIds: contentIds || ["LymphDrainage"],
          contentType: contentType || "subscription",
          value: value || 29.99,
          currency: currency || "USD",
        };

        // Log event initiation
        logPixelEvent("AddToCart", {
          source: "frontend",
          pixelId,
          email,
          ...trackingData,
          fbclid: cookies.fbclid || undefined,
          fbp: cookies.fbp || undefined,
          fbc: cookies.fbc || undefined,
        });

        // Enhanced frontend logging
        console.log("🛍️ [FRONTEND] Tracking AddToCart with configuration:", {
          pixelId,
          email: email ? "[REDACTED]" : "none",
          ...trackingData,
          fbclid: cookies.fbclid,
          fbp: cookies.fbp ? "[PRESENT]" : "none",
          fbc: cookies.fbc ? "[PRESENT]" : "none",
          timestamp: new Date().toISOString(),
        });

        // Also track on frontend pixel for immediate feedback
        if (window.fbq) {
          window.fbq("track", "AddToCart", {
            content_ids: trackingData.contentIds,
            content_type: trackingData.contentType,
            value: trackingData.value,
            currency: trackingData.currency,
          });
          console.log("🎯 [FRONTEND] AddToCart tracked via browser pixel");
        }

        const response = await fetch("/api/meta-pixel/track-add-to-cart", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            data: {
              email,
              fbp: cookies.fbp,
              fbc: cookies.fbc,
              fbclid: cookies.fbclid,
              ...trackingData,
              pixelId,
              // Include additional user data if available
              ...(additionalUserData?.firstName && {
                firstName: additionalUserData.firstName,
              }),
              ...(additionalUserData?.lastName && {
                lastName: additionalUserData.lastName,
              }),
              ...(additionalUserData?.phone && {
                phone: additionalUserData.phone,
              }),
              ...(additionalUserData?.city && {
                city: additionalUserData.city,
              }),
              ...(additionalUserData?.state && {
                state: additionalUserData.state,
              }),
              ...(additionalUserData?.country && {
                country: additionalUserData.country,
              }),
              ...(additionalUserData?.zip && { zip: additionalUserData.zip }),
              ...(additionalUserData?.dateOfBirth && {
                dateOfBirth: additionalUserData.dateOfBirth,
              }),
              ...(additionalUserData?.externalId && {
                externalId: additionalUserData.externalId,
              }),
            },
          }),
        });

        if (!response.ok) {
          throw new Error(
            `HTTP ${response.status}: Failed to track AddToCart event`
          );
        }

        const result = await response.json();
        console.log(
          "✅ [BACKEND] AddToCart event tracked successfully:",
          result
        );

        // Log success
        logPixelSuccess("AddToCart", {
          source: "backend",
          pixelId,
          email,
          metaResponse: result,
        });
      } catch (error) {
        console.error("❌ [ERROR] Error tracking AddToCart event:", error);

        // Log error
        logPixelError(
          "AddToCart",
          error instanceof Error ? error.message : "Unknown error",
          {
            source: "backend",
            pixelId: getPixelId(),
            email,
          }
        );

        // Don't throw error to prevent breaking user flow
      }
    },
    []
  );

  return {
    trackPageView,
    trackInitiateCheckout,
    trackPurchase,
    trackCustomEvent,
    trackAddToCart,
  };
};

export default useFacebookPixel;
