import { useState, useCallback } from 'react';

export const useBMICheck = () => {
    const [bmiMessage, setBmiMessage] = useState<string>('');

    const calculateBMI = (height: number, weight: number): number => {
        return weight / ((height / 100) ** 2);
    };

    const getBMICategory = (bmi: number): string => {
        if (bmi < 18.5) return 'underweight';
        if (bmi < 25) return 'normal weight';
        if (bmi < 30) return 'overweight';
        if (bmi < 35) return 'obese class I';
        if (bmi < 40) return 'obese class II';
        return 'severely obese';
    };

    const checkBMI = useCallback((height1: string, height2: string, weight: string, desiredWeight: string, unit: 'imperial' | 'metric') => {
        if (!height1 || !weight || !desiredWeight || (unit === 'imperial' && !height2)) {
            setBmiMessage('Please provide all required inputs.');
            return;
        }

        let heightInCm: number;
        let weightInKg: number;
        let desiredWeightInKg: number;

        if (unit === 'imperial') {
            heightInCm = (parseFloat(height1) * 30.48) + (parseFloat(height2 || '0') * 2.54);
            weightInKg = parseFloat(weight) * 0.453592;
            desiredWeightInKg = parseFloat(desiredWeight) * 0.453592;
        } else {
            heightInCm = parseFloat(height1);
            weightInKg = parseFloat(weight);
            desiredWeightInKg = parseFloat(desiredWeight);
        }

        if (isNaN(heightInCm) || isNaN(weightInKg) || isNaN(desiredWeightInKg)) {
            setBmiMessage('Please enter valid numerical values.');
            return;
        }

        const currentBMI = parseFloat(calculateBMI(heightInCm, weightInKg).toFixed(1));
        const desiredBMI = parseFloat(calculateBMI(heightInCm, desiredWeightInKg).toFixed(1));

        const currentCategory = getBMICategory(currentBMI);
        const desiredCategory = getBMICategory(desiredBMI);

        if (currentBMI === desiredBMI) {
            setBmiMessage(`Your current BMI (${currentBMI}) is in the ${currentCategory} range.`);
            return;
        }

        if (desiredBMI < 18.5) {
            setBmiMessage(`Your current BMI (${currentBMI}) is in the ${currentCategory} range, but your goal BMI (${desiredBMI}) would be considered underweight. Please reconsider your goals.`);
        } else if (desiredBMI > 24.9) {
            setBmiMessage(`Your current BMI (${currentBMI}) is in the ${currentCategory} range, but your goal BMI (${desiredBMI}) would be considered ${desiredCategory}. Please reconsider your goals.`);
        } else {
            setBmiMessage(`Your current BMI (${currentBMI}) is in the ${currentCategory} range, and your goal BMI (${desiredBMI}) is in the healthy range.`);
        }
    }, []);

    return { checkBMI, bmiMessage };
};