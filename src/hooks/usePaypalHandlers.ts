import { useState } from 'react';
import { countries } from 'countries-list';
import { ShippingState } from '../app/redux/shippingslice';

interface PlanConfig {
    interval_unit: 'MONTH';
    interval_count: number;
    price: string;
}

interface UsePayPalHandlers {
    handlePayPalSubscription: () => Promise<void>;
    handleOneTimePayPalPayment: () => Promise<void>;
    isLoading: boolean;
    error: string | null;
}

interface PayPalHookProps {
    title: string;
    amount: number;
    quantity: number;
    isFirstSubscription: boolean;
    productId?: string;
    shippingDetails: ShippingState;
    totalAmount: number;
    discountAmount: number;
}

const getCountryCode = (countryName: string): string => {
    const countryEntry = Object.entries(countries).find(
        entry => entry[1].name === countryName
    );
    return countryEntry ? countryEntry[0] : 'US';
};

const getPlanConfig = (title: string, amount: number, isFirstSubscription: boolean): PlanConfig => {
    if (isFirstSubscription) {
        amount = amount - (amount * 0.1);
    }
    if (title.toLowerCase().includes('1 month')) {
        return {
            interval_unit: 'MONTH',
            interval_count: 1,
            price: amount.toString()
        };
    } else if (title.toLowerCase().includes('3 month')) {
        return {
            interval_unit: 'MONTH',
            interval_count: 3,
            price: amount.toString()
        };
    } else if (title.toLowerCase().includes('6 month')) {
        return {
            interval_unit: 'MONTH',
            interval_count: 6,
            price: amount.toString()
        };
    }
    return {
        interval_unit: 'MONTH',
        interval_count: 1,
        price: amount.toString()
    };
};

export const usePayPalHandlers = ({
    title,
    amount,
    quantity,
    isFirstSubscription,
    productId,
    shippingDetails,
    totalAmount,
    discountAmount
}: PayPalHookProps): UsePayPalHandlers => {
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);

    const createPayPalPlan = async () => {
        try {
            const planConfig = getPlanConfig(title, totalAmount, isFirstSubscription);
            const bottleCount = planConfig.interval_count === 1 ? '1' :
                planConfig.interval_count === 3 ? '3' : '6';

            const unitPrice = planConfig.price;

            const planData = {
                product_id: productId,
                name: `${bottleCount}x ColonFit`,
                description: `${bottleCount} bottle${bottleCount === '1' ? '' : 's'} of ColonFit`,
                status: "ACTIVE",
                billing_cycles: [
                    {
                        frequency: {
                            interval_unit: planConfig.interval_unit,
                            interval_count: planConfig.interval_count
                        },
                        tenure_type: "REGULAR",
                        sequence: 1,
                        total_cycles: 0,
                        pricing_scheme: {
                            fixed_price: {
                                value: unitPrice,
                                currency_code: "USD"
                            }
                        }
                    }
                ],
                payment_preferences: {
                    auto_bill_outstanding: true,
                    setup_fee_failure_action: "CONTINUE",
                    payment_failure_threshold: 3
                }
            };

            const response = await fetch('/api/paypal/create-plan', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(planData)
            });

            if (!response.ok) {
                throw new Error('Failed to create PayPal plan');
            }

            const data = await response.json();
            return data.id;
        } catch (error) {
            console.error('Error creating PayPal plan:', error);
            throw new Error('Failed to create PayPal plan');
        }
    };

    const handlePayPalSubscription = async () => {
        try {
            setIsLoading(true);
            setError(null);

            const planId = await createPayPalPlan();
            const countryCode = getCountryCode(shippingDetails.country);

            const subscriptionData = {
                plan_id: planId,
                start_time: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
                quantity: 1,
                shipping_amount: {
                    currency_code: "USD",
                    value: "0.00"
                },
                subscriber: {
                    name: {
                        given_name: shippingDetails.firstName,
                        surname: shippingDetails.lastName
                    },
                    email_address: shippingDetails.email,
                    shipping_address: {
                        name: {
                            full_name: `${shippingDetails.firstName} ${shippingDetails.lastName}`
                        },
                        address: {
                            address_line_1: shippingDetails.streetAddress,
                            address_line_2: shippingDetails.apartment || '',
                            admin_area_2: shippingDetails.city,
                            admin_area_1: shippingDetails.state,
                            postal_code: shippingDetails.zipCode,
                            country_code: countryCode
                        }
                    }
                },
                application_context: {
                    brand_name: "ColonFit",
                    locale: "en-US",
                    shipping_preference: "SET_PROVIDED_ADDRESS",
                    user_action: "SUBSCRIBE_NOW",
                    payment_method: {
                        payer_selected: "PAYPAL",
                        payee_preferred: "IMMEDIATE_PAYMENT_REQUIRED"
                    },
                    return_url: process.env.NODE_ENV === 'production'
                        ? 'https://colon.fit/paypal/thankyou'
                        : 'http://localhost:3000/paypal/thankyou',
                    cancel_url: process.env.NODE_ENV === 'production'
                        ? 'https://colon.fit/paypal/cancel'
                        : 'http://localhost:3000/paypal/cancel'
                }
            };

            const response = await fetch('/api/paypal/create-subscription', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(subscriptionData)
            });

            const data = await response.json();

            if (!response.ok) {
                throw new Error(data.message || 'Failed to create PayPal subscription');
            }

            const approvalLink = data.links.find((link: any) => link.rel === "approve");

            if (approvalLink) {
                window.location.href = approvalLink.href;
            } else {
                throw new Error('PayPal approval URL not found');
            }
        } catch (error) {
            console.error('Error creating PayPal subscription:', error);
            setError('Failed to initialize PayPal subscription');
        } finally {
            setIsLoading(false);
        }
    };

    const handleOneTimePayPalPayment = async () => {
        try {
            setIsLoading(true);
            setError(null);
            const countryCode = getCountryCode(shippingDetails.country);

            const response = await fetch('/api/paypal/create-paypal-order', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    intent: 'CAPTURE',
                    purchase_units: [
                        {
                            amount: {
                                currency_code: 'USD',
                                value: totalAmount.toString(),
                                breakdown: {
                                    item_total: {
                                        currency_code: 'USD',
                                        value: amount.toString()
                                    },
                                    discount: {
                                        currency_code: 'USD',
                                        value: discountAmount.toString()
                                    },
                                    shipping: {
                                        currency_code: 'USD',
                                        value: '0.00'
                                    }
                                }
                            },
                            items: [
                                {
                                    name: title,
                                    quantity: quantity.toString(),
                                    unit_amount: {
                                        currency_code: 'USD',
                                        value: (amount / quantity).toString()
                                    }
                                }
                            ],
                            shipping: {
                                name: {
                                    full_name: `${shippingDetails.firstName} ${shippingDetails.lastName}`
                                },
                                address: {
                                    address_line_1: shippingDetails.streetAddress,
                                    address_line_2: shippingDetails.apartment || '',
                                    admin_area_2: shippingDetails.city,
                                    admin_area_1: shippingDetails.state,
                                    postal_code: shippingDetails.zipCode,
                                    country_code: countryCode
                                }
                            }
                        }
                    ],
                    application_context: {
                        brand_name: "ColonFit",
                        shipping_preference: "SET_PROVIDED_ADDRESS",
                        user_action: "PAY_NOW",
                        return_url: process.env.NODE_ENV === 'production'
                            ? 'https://colon.fit/paypal/thankyou'
                            : 'http://localhost:3000/paypal/thankyou',
                        cancel_url: process.env.NODE_ENV === 'production'
                            ? 'https://colon.fit/paypal/cancel'
                            : 'http://localhost:3000/paypal/cancel'
                    }
                })
            });

            const data = await response.json();

            if (!response.ok) {
                throw new Error(data.message || 'Failed to create PayPal order');
            }

            const approvalLink = data.links.find((link: any) => link.rel === "approve");

            if (approvalLink) {
                window.location.href = approvalLink.href;
            } else {
                throw new Error('PayPal approval URL not found');
            }

        } catch (error) {
            console.error('Error creating PayPal order:', error);
            setError('Failed to initialize PayPal payment');
        } finally {
            setIsLoading(false);
        }
    };

    return {
        handlePayPalSubscription,
        handleOneTimePayPalPayment,
        isLoading,
        error
    };
};