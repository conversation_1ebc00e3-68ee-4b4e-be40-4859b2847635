import React, { useState, useRef } from "react";
import Image from "next/image";

interface Slide {
  imageUrl: string;
}

interface FullImageCarouselProps {
  slides: Slide[];
}

const FullImageCarousel: React.FC<FullImageCarouselProps> = ({ slides }) => {
  const [current, setCurrent] = useState(0);
  const [isAnimating, setIsAnimating] = useState(false);
  const total = slides.length;
  const carouselRef = useRef<HTMLDivElement>(null);

  const goTo = (idx: number) => {
    if (isAnimating) return;
    setCurrent((idx + total) % total);
    setIsAnimating(true);
    setTimeout(() => setIsAnimating(false), 300); // mimic animation lock
  };

  // Touch navigation
  let startX = 0;
  const handleTouchStart = (e: React.TouchEvent) => {
    startX = e.touches[0].clientX;
  };
  const handleTouchEnd = (e: React.TouchEvent) => {
    const endX = e.changedTouches[0].clientX;
    if (startX - endX > 50) goTo(current + 1);
    if (endX - startX > 50) goTo(current - 1);
  };

  return (
    <div
      className="relative w-full aspect-square max-w-lg mx-auto select-none"
      ref={carouselRef}
      onTouchStart={handleTouchStart}
      onTouchEnd={handleTouchEnd}
      tabIndex={0}
      aria-roledescription="carousel"
      aria-label="Images"
    >
      {/* Image only, no border/bg */}
      <div className="w-full h-full relative">
        <Image
          src={slides[current].imageUrl}
          alt=""
          fill
          className="object-contain"
          priority
          sizes="(max-width: 600px) 100vw, 600px"
        />
      </div>
      {/* Navigation */}
      <button
        className="absolute left-2 top-1/2 -translate-y-1/2 bg-white/80 rounded-full p-2 shadow"
        onClick={() => goTo(current - 1)}
        aria-label="Previous Slide"
        disabled={isAnimating}
        tabIndex={0}
      >
        &#8592;
      </button>
      <button
        className="absolute right-2 top-1/2 -translate-y-1/2 bg-white/80 rounded-full p-2 shadow"
        onClick={() => goTo(current + 1)}
        aria-label="Next Slide"
        disabled={isAnimating}
        tabIndex={0}
      >
        &#8594;
      </button>
      {/* Dots */}
      <div className="flex justify-center mt-2 gap-2 absolute left-0 right-0 bottom-2 z-10">
        {slides.map((_, idx) => (
          <button
            key={idx}
            className={`w-2 h-2 rounded-full ${idx === current ? "bg-green-600" : "bg-gray-300"}`}
            onClick={() => goTo(idx)}
            aria-label={`Go to slide ${idx + 1}`}
            disabled={isAnimating}
            tabIndex={0}
          />
        ))}
      </div>
    </div>
  );
};

export default FullImageCarousel; 