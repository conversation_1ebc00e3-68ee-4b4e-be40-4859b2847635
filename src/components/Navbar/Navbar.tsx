import React from 'react';
import Link from 'next/link';
import Image from 'next/image';
import logo from '../../../public/logo/CF LOGO PNG.png'

interface NavbarProps {
    currentQuestion: number;
    totalQuestions: number;
}

const Navbar: React.FC<NavbarProps> = ({ currentQuestion, totalQuestions }) => {
    const progress = (currentQuestion / totalQuestions) * 100;

    return (
        <nav className="fixed top-0 left-0 right-0 bg-white shadow-md z-50">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div className="flex justify-between items-center py-4">
                    <div className="flex items-center">
                        <Link href="/" className="font-bold text-xl text-black">
                            <Image
                                src={logo}
                                alt="ColonFit Logo"
                                width={150}
                                height={50}
                                objectFit="contain"
                            />
                        </Link>
                    </div>
                    <div className="text-black font-medium">
                        {currentQuestion} of {totalQuestions}
                    </div>
                </div>
            </div>
            <div className="h-1 bg-gray-200">
                <div
                    className="h-full transition-all duration-300 ease-in-out"
                    style={{ width: `${progress}%`, backgroundColor: '#008ab8' }}
                ></div>
            </div>
        </nav>
    );
};

export default Navbar;