"use client";
import React, { useState } from "react";
import { useRouter } from "next/navigation";
import { QuizQuestion } from "@/data/quiz-data";

interface ReferralPageProps {
  question: QuizQuestion;
}

export default function ReferralPage({ question }: ReferralPageProps) {
  const [email, setEmail] = useState("");
  const router = useRouter();

  const handleDone = () => {
    router.push(question.nextPage);
  };

  const handleSkip = () => {
    router.push("/quiz/gender");
  };

  return (
    <div className="flex flex-col min-h-screen bg-white">
      {/* Green header with title and subheading */}
      <div className="w-full bg-gradient-to-br from-[#184C43] to-[#256d5a] rounded-b-3xl px-4 pt-8 pb-10 md:pb-14 text-white relative overflow-hidden flex flex-col items-center">
        {/* Decorative icons */}
        <div className="absolute left-8 top-16 opacity-40 z-0">
          <svg width="32" height="32">
            <circle cx="16" cy="16" r="2" fill="#B6CFC7" />
            <line x1="16" y1="0" x2="16" y2="32" stroke="#B6CFC7" strokeWidth="1.5" />
            <line x1="0" y1="16" x2="32" y2="16" stroke="#B6CFC7" strokeWidth="1.5" />
          </svg>
        </div>
        <div className="absolute right-8 top-10 opacity-40 z-0">
          <svg width="32" height="32">
            <circle cx="16" cy="16" r="2" fill="#B6CFC7" />
            <line x1="16" y1="0" x2="16" y2="32" stroke="#B6CFC7" strokeWidth="1.5" />
            <line x1="0" y1="16" x2="32" y2="16" stroke="#B6CFC7" strokeWidth="1.5" />
          </svg>
        </div>
        {/* Progress bar */}
        <div className="w-full max-w-lg mx-auto mb-6">
          <div className="flex justify-between mb-2">
            <span className="text-sm text-white/70"> </span>
            <span className="text-sm text-white/80 font-medium">{question.progress}%</span>
          </div>
          <div className="h-2 bg-white/20 rounded-full overflow-hidden">
            <div className="h-full bg-[#c6e48b]" style={{ width: `${question.progress}%` }}></div>
          </div>
        </div>
        {/* Title and subheading */}
        <div className="w-full max-w-lg mx-auto text-center z-10">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-3 leading-tight">
            {question.title}
          </h2>
          <p className="text-white/70 text-base md:text-lg mb-2">
            {question.subheading}
          </p>
        </div>
      </div>
      {/* White section with card and input */}
      <div className="flex-1 w-full flex flex-col items-center bg-white px-4 pt-8 pb-16">
        <div className="w-full max-w-md mx-auto bg-[#F5F5F5] rounded-3xl flex flex-col items-center justify-center p-8 mb-8 shadow">
          <div className="text-center font-bold text-lg md:text-xl text-[#184C43] mb-2">
            {question.cardTitle}
          </div>
          <div className="text-center text-[#6B7A7A] text-base md:text-lg mb-6">
            {question.cardSub}
          </div>
          <input
            type="email"
            placeholder="Referral Email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            className="w-full mb-6 px-4 py-3 rounded-full border border-gray-300 focus:outline-none focus:ring-2 focus:ring-[#184C43] text-center text-lg bg-white"
          />
          <button
            className="w-full py-3 rounded-full font-medium transition-all flex items-center justify-center text-lg bg-[#184C43] text-white hover:bg-[#256d5a] mb-4"
            onClick={handleDone}
          >
            Done
          </button>
          <button
            className="w-full py-3 rounded-full font-medium transition-all flex items-center justify-center text-lg bg-gray-300 text-[#184C43] hover:bg-gray-400"
            onClick={handleSkip}
          >
            Skip
          </button>
        </div>
        <div className="w-full max-w-md mx-auto text-center text-[#6B7A7A] text-xs mt-2">
          {question.privacy}
        </div>
      </div>
    </div>
  );
} 