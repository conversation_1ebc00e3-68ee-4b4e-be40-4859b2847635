'use client'

// In CheckoutContent.tsx

import React, { useState } from 'react';
import ShippingForm from '@/components/Checkout/ShippingForm';
import PaymentForm from '@/components/Checkout/PaymentForm';
import ReceiptPage from '@/components/Checkout/ReceiptPage';
import { useAppSelector } from '../../app/redux/hooks';

interface ShippingData {
    firstName: string;
    lastName: string;
    email: string;
    streetAddress: string;
    apartment?: string;
    city: string;
    state: string;
    zipCode: string;
    country: string;
}

interface CheckoutContentProps {
    activeTab: string;
    shippingData: ShippingData | null;
    onContinueToPayment: (data: ShippingData) => void;
    onPaymentSuccess: () => void;
}

const CheckoutContent: React.FC<CheckoutContentProps> = ({
    activeTab,
    shippingData,
    onContinueToPayment,
    onPaymentSuccess
}) => {
    const { amount, title } = useAppSelector((state) => state.order);
    const [transactionId, setTransactionId] = useState<string | null>(null);

    const handlePaymentSuccess = (paymentIntentId: string) => {
        setTransactionId(paymentIntentId);
        onPaymentSuccess();
    };

    return (
        <>
            {activeTab === 'shipping' && (
                <ShippingForm
                    onContinue={onContinueToPayment}
                    amount={amount}
                />
            )}

            {activeTab === 'payment' && (
                <PaymentForm
                    onSuccess={handlePaymentSuccess}
                    plan={title}
                    price={amount}
                />
            )}

            {activeTab === 'receipt' && shippingData && transactionId && (
                <ReceiptPage
                    amount={amount}
                    shippingData={shippingData}
                    transactionId={transactionId}
                />
            )}
        </>
    );
};

export default CheckoutContent;