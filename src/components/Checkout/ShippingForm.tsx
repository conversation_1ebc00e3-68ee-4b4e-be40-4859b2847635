'use client'
import React, { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { countries } from 'countries-list';
import useLocalStorage from '@/hooks/useLocalStorage';
import Link from 'next/link';
import { useAppDispatch, useAppSelector } from '@/app/redux/hooks';
import { setShippingDetails, setAgreeToSMS } from '@/app/redux/shippingslice';



type FormData = {
    firstName: string;
    lastName: string;
    streetAddress: string;
    apartment?: string;
    country: string;
    state: string;
    city: string;
    zipCode: string;
    email: string;
    phone: string;
};

interface ShippingFormProps {
    onContinue: (data: FormData & { agreeToSMS: boolean }) => void;
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    amount: number;
}

// eslint-disable-next-line @typescript-eslint/no-unused-vars
const ShippingForm: React.FC<ShippingFormProps> = ({ onContinue, amount }) => {
    const { register, handleSubmit, setValue, formState: { errors } } = useForm<FormData>();
    const [savedEmail] = useLocalStorage('userEmail', '');
    const dispatch = useAppDispatch();
    const shippingState = useAppSelector(state => state.shipping);

    useEffect(() => {
        if (savedEmail) {
            setValue('email', savedEmail);
        }
        fetch('https://ipapi.co/json/')
            .then(res => res.json())
            .then(data => {
                setValue('country', data.country_name);
                setValue('phone', `${data.country_calling_code}`);
            })
            .catch(error => console.error('Error fetching location:', error));
    }, [setValue, savedEmail]);

    useEffect(() => {
        Object.entries(shippingState).forEach(([key, value]) => {
            if (key !== 'agreeToSMS' && value !== '') {
                setValue(key as keyof FormData, value);
            }
        });
    }, [shippingState, setValue]);

    const onSubmit = (data: FormData) => {
        const formData = {
            ...data,
            agreeToSMS: shippingState.agreeToSMS
        };
        dispatch(setShippingDetails(formData));
        localStorage.setItem('shippingDetails', JSON.stringify(formData));
        onContinue(formData);
    };

    const getErrorMessage = (fieldName: keyof FormData): string | undefined => {
        const error = errors[fieldName];
        if (error && 'message' in error) {
            return error.message as string;
        }
        return undefined;
    };

    return (
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <input
                        {...register('firstName', { required: 'First name is required' })}
                        placeholder="First Name"
                        className="w-full p-3 border rounded-lg text-sm"
                        defaultValue={shippingState.firstName}
                    />
                    {getErrorMessage('firstName') && <span className="text-red-500 text-xs mt-1">{getErrorMessage('firstName')}</span>}
                </div>
                <div>
                    <input
                        {...register('lastName', { required: 'Last name is required' })}
                        placeholder="Last Name"
                        className="w-full p-3 border rounded-lg text-sm"
                        defaultValue={shippingState.lastName}
                    />
                    {getErrorMessage('lastName') && <span className="text-red-500 text-xs mt-1">{getErrorMessage('lastName')}</span>}
                </div>
            </div>

            <input
                {...register('streetAddress', { required: 'Street address is required' })}
                placeholder="Street Address"
                className="w-full p-3 border rounded-lg text-sm"
                defaultValue={shippingState.streetAddress}
            />
            {getErrorMessage('streetAddress') && <span className="text-red-500 text-xs mt-1">{getErrorMessage('streetAddress')}</span>}

            <input
                {...register('apartment')}
                placeholder="Apartment, suite, etc. (optional)"
                className="w-full p-3 border rounded-lg text-sm"
                defaultValue={shippingState.apartment}
            />

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <select
                        {...register('country', { required: 'Country is required' })}
                        className="w-full p-3 border rounded-lg text-sm"
                        defaultValue={shippingState.country}
                    >
                        <option value="">Select Country</option>
                        {Object.entries(countries).map(([code, country]) => (
                            <option key={code} value={country.name}>
                                {country.name}
                            </option>
                        ))}
                    </select>
                    {getErrorMessage('country') && <span className="text-red-500 text-xs mt-1">{getErrorMessage('country')}</span>}
                </div>
                <input
                    {...register('state', { required: 'State is required' })}
                    placeholder="State"
                    className="w-full p-3 border rounded-lg text-sm"
                    defaultValue={shippingState.state}
                />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <input
                    {...register('city', { required: 'City is required' })}
                    placeholder="Town/City"
                    className="w-full p-3 border rounded-lg text-sm"
                    defaultValue={shippingState.city}
                />
                <input
                    {...register('zipCode', { required: 'ZIP code is required' })}
                    placeholder="ZIP Code"
                    className="w-full p-3 border rounded-lg text-sm"
                    defaultValue={shippingState.zipCode}
                />
            </div>

            <div className="relative">
                <input
                    {...register('email', { required: 'Email is required', pattern: { value: /^\S+@\S+$/i, message: 'Invalid email address' } })}
                    defaultValue={savedEmail || shippingState.email}
                    placeholder="Email"
                    className="w-full p-3 border rounded-lg text-sm bg-gray-100"
                    disabled
                />
                {getErrorMessage('email') && <span className="text-red-500 text-xs mt-1">{getErrorMessage('email')}</span>}
                <p className="text-xs text-gray-500 mt-1">Email cannot be changed</p>
            </div>

            <input
                {...register('phone', { required: 'Phone number is required' })}
                placeholder="Phone Number (with country code)"
                className="w-full p-3 border rounded-lg text-sm"
                defaultValue={shippingState.phone}
            />
            {getErrorMessage('phone') && <span className="text-red-500 text-xs mt-1">{getErrorMessage('phone')}</span>}
            <div className="flex items-start space-x-3">
                <input
                    type="checkbox"
                    id="smsConsent"
                    checked={shippingState.agreeToSMS}
                    onChange={(e) => dispatch(setAgreeToSMS(e.target.checked))}
                    className="mt-1"
                />
                <label htmlFor="smsConsent" className="text-xs text-gray-600">
                    I want to get special deals and discounts from ColonFit by SMS.
                    <br />
                    <br />
                    By checking the box, you agree to receive marketing text messages from ColonFit at the number provided, including messages sent by autodialer.  Check our{' '}
                    <Link href="/privacy-policy" className="text-blue-600 hover:underline">
                        Privacy Policy
                    </Link>{' '}
                    and{' '}
                    <Link href="/terms" className="text-blue-600 hover:underline">
                        Terms and Conditions
                    </Link>
                    .
                </label>
            </div>

            <button
                type="submit"
                className="w-full sm:w-auto px-8 py-3 bg-[#639db1] text-white rounded-full hover:bg-[#006a8f] transition duration-300 text-lg font-semibold block mx-auto"
            >
                Continue to Payment
            </button>
        </form>
    );
};

export default ShippingForm;