import React, { useState, useEffect } from "react";
import {
  useStripe,
  useElements,
  PaymentElement,
  Elements,
} from "@stripe/react-stripe-js";
import { loadStripe } from "@stripe/stripe-js";
import { ChevronDown, CreditCard } from "lucide-react";
import { PayPalScriptProvider } from "@paypal/react-paypal-js";
import Image from "next/image";
import cards from "../../../public/cards2.png";
import { useAppSelector, useAppDispatch } from "../../app/redux/hooks";
import { setClientSecret } from "../../app/redux/clientSecretSlice";
import useFacebookPixel from "@/hooks/useFacebookPixel";
import { countries } from "countries-list";
import useLocalStorage from "@/hooks/useLocalStorage";

const stripePromise = loadStripe(
  process.env.NODE_ENV === "production"
    ? process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY_LIVE!
    : process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY_TEST!
);

interface PlanConfig {
  interval_unit: "MONTH";
  interval_count: number;
  price: string;
}

const getCountryCode = (countryName: string): string => {
  const countryEntry = Object.entries(countries).find(
    (entry) => entry[1].name === countryName
  );
  return countryEntry ? countryEntry[0] : "US";
};

const getPlanConfig = (
  title: string,
  amount: number,
  isFirstSubscription: boolean
): PlanConfig => {
  if (isFirstSubscription) {
    amount = Number((amount - amount * 0.1).toFixed(2));
  }
  if (title.toLowerCase().includes("1-month")) {
    return {
      interval_unit: "MONTH",
      interval_count: 1,
      price: amount.toString(),
    };
  } else if (title.toLowerCase().includes("3-month")) {
    return {
      interval_unit: "MONTH",
      interval_count: 3,
      price: amount.toString(),
    };
  } else if (title.toLowerCase().includes("6-month")) {
    return {
      interval_unit: "MONTH",
      interval_count: 6,
      price: amount.toString(),
    };
  }
  return {
    interval_unit: "MONTH",
    interval_count: 1,
    price: amount.toString(),
  };
};

interface PaymentFormProps {
  onSuccess: (transactionId: string) => void;
  plan: string;
  price: number;
}

const PaymentForm: React.FC<PaymentFormProps> = ({
  onSuccess,
  plan,
  price,
}) => {
  const [error, setError] = useState<string | null>(null);
  const [isCartOpen, setIsCartOpen] = useState(false);
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<
    "card" | "paypal" | null
  >(null);
  const [isLoading, setIsLoading] = useState(false);
  const [couponCode, setCouponCode] = useState("FIRSTBUY");
  const [isCouponApplied, setIsCouponApplied] = useState(false);
  const [discountAmount, setDiscountAmount] = useState(0);
  const [clientSecret, setClientSecret] = useState<string | null>(null);
  const [hasFetchedClientSecret, setHasFetchedClientSecret] = useState(false);
  const dispatch = useAppDispatch();
  const {
    title,
    amount,
    quantity,
    isFirstSubscription,
    productId,
    isSubscription,
  } = useAppSelector((state) => state.order);
  const customerId = useAppSelector((state) => state.customer.id);
  const shippingDetails = useAppSelector((state) => state.shipping);
  const [totalAmount, setTotalAmount] = useState(amount);
  const [savedEmail] = useLocalStorage("userEmail", "");
  const { trackPurchase } = useFacebookPixel();
  const isOneTimePayment = !isSubscription;
  console.log(isOneTimePayment);
  const PLAN_PRICE_IDS: Record<string, string> = {
    "7day": process.env.NEXT_PUBLIC_STRIPE_PRICE_ID_7DAY || "price_1RX2qNP0M1wjprtStXLC5C2I",
    "1month": process.env.NEXT_PUBLIC_STRIPE_PRICE_ID_1MONTH || "price_1RX2qsP0M1wjprtSHMOEKVcW",
    "3month": process.env.NEXT_PUBLIC_STRIPE_PRICE_ID_3MONTH || "price_1RX2rsP0M1wjprtSLGvisi8t",
  };

  const createPayPalPlan = async () => {
    try {
      const planConfig = getPlanConfig(title, totalAmount, isFirstSubscription);
      const bottleCount =
        planConfig.interval_count === 1
          ? "1"
          : planConfig.interval_count === 3
          ? "3"
          : "6";

      const unitPrice = planConfig.price;
      const planData = {
        product_id: productId,
        name: `${bottleCount}x ColonFit`,
        description: `${bottleCount} bottle${
          bottleCount === "1" ? "" : "s"
        } of ColonFit`,
        status: "ACTIVE",
        billing_cycles: [
          {
            frequency: {
              interval_unit: planConfig.interval_unit,
              interval_count: planConfig.interval_count,
            },
            tenure_type: "REGULAR",
            sequence: 1,
            total_cycles: 0,
            pricing_scheme: {
              fixed_price: {
                value: unitPrice,
                currency_code: "USD",
              },
            },
          },
        ],
        payment_preferences: {
          auto_bill_outstanding: true,
          setup_fee_failure_action: "CONTINUE",
          payment_failure_threshold: 3,
        },
      };

      const response = await fetch("/api/create-plan", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(planData),
      });

      if (!response.ok) {
        throw new Error("Failed to create PayPal plan");
      }

      const data = await response.json();
      return data.id;
    } catch (error) {
      console.error("Error creating PayPal plan:", error);
      throw new Error("Failed to create PayPal plan");
    }
  };

  const handlePayPalSubscription = async () => {
    try {
      setIsLoading(true);

      const planId = await createPayPalPlan();
      const countryCode = getCountryCode(shippingDetails.country);

      const subscriptionData = {
        plan_id: planId,
        start_time: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
        quantity: 1,
        shipping_amount: {
          currency_code: "USD",
          value: "0.00",
        },
        subscriber: {
          name: {
            given_name: shippingDetails.firstName,
            surname: shippingDetails.lastName,
          },
          email_address: shippingDetails.email,
          shipping_address: {
            name: {
              full_name: `${shippingDetails.firstName} ${shippingDetails.lastName}`,
            },
            address: {
              address_line_1: shippingDetails.streetAddress,
              address_line_2: shippingDetails.apartment || "",
              admin_area_2: shippingDetails.city,
              admin_area_1: shippingDetails.state,
              postal_code: shippingDetails.zipCode,
              country_code: countryCode,
            },
          },
        },
        application_context: {
          brand_name: "ColonFit",
          locale: "en-US",
          shipping_preference: "SET_PROVIDED_ADDRESS",
          user_action: "SUBSCRIBE_NOW",
          payment_method: {
            payer_selected: "PAYPAL",
            payee_preferred: "IMMEDIATE_PAYMENT_REQUIRED",
          },
          return_url:
            process.env.NODE_ENV === "production"
              ? "https://colon.fit/paypal/thankyou"
              : "http://localhost:3000/paypal/thankyou",
          cancel_url:
            process.env.NODE_ENV === "production"
              ? "https://colon.fit/paypal/cancel"
              : "http://localhost:3000/paypal/cancel",
        },
      };

      const response = await fetch("/api/create-subscription", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(subscriptionData),
      });

      const data = await response.json();
      console.log(data);
      if (!response.ok) {
        throw new Error(data.message || "Failed to create PayPal subscription");
      }

      const approvalLink = data.links.find(
        (link: any) => link.rel === "approve"
      );

      if (approvalLink) {
        window.location.href = approvalLink.href;
        await trackPurchase(amount, "USD", ["ColonFit"], title, savedEmail);
      } else {
        throw new Error("PayPal approval URL not found");
      }
    } catch (error) {
      console.error("Error creating PayPal subscription:", error);
      setError("Failed to initialize PayPal subscription");
    } finally {
      setIsLoading(false);
    }
  };

  const handleOneTimePayPalPayment = async () => {
    try {
      setIsLoading(true);
      const countryCode = getCountryCode(shippingDetails.country);

      const response = await fetch("/api/create-paypal-order", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          intent: "CAPTURE",
          purchase_units: [
            {
              amount: {
                currency_code: "USD",
                value: totalAmount.toString(),
                breakdown: {
                  item_total: {
                    currency_code: "USD",
                    value: amount.toString(),
                  },
                  discount: {
                    currency_code: "USD",
                    value: discountAmount.toString(),
                  },
                  shipping: {
                    currency_code: "USD",
                    value: "0.00",
                  },
                },
              },
              items: [
                {
                  name: title,
                  quantity: quantity.toString(),
                  unit_amount: {
                    currency_code: "USD",
                    value: (amount / quantity).toString(),
                  },
                },
              ],
              shipping: {
                name: {
                  full_name: `${shippingDetails.firstName} ${shippingDetails.lastName}`,
                },
                address: {
                  address_line_1: shippingDetails.streetAddress,
                  address_line_2: shippingDetails.apartment || "",
                  admin_area_2: shippingDetails.city,
                  admin_area_1: shippingDetails.state,
                  postal_code: shippingDetails.zipCode,
                  country_code: countryCode,
                },
              },
            },
          ],
          application_context: {
            brand_name: "ColonFit",
            shipping_preference: "SET_PROVIDED_ADDRESS",
            user_action: "PAY_NOW",
            return_url:
              process.env.NODE_ENV === "production"
                ? "https://colon.fit/paypal/thankyou"
                : "http://localhost:3000/paypal/thankyou",
            cancel_url:
              process.env.NODE_ENV === "production"
                ? "https://colon.fit/paypal/cancel"
                : "http://localhost:3000/paypal/cancel",
          },
        }),
      });

      const data = await response.json();
      console.log(data);
      if (!response.ok) {
        throw new Error(data.message || "Failed to create PayPal order");
      }

      const approvalLink = data.links.find(
        (link: any) => link.rel === "approve"
      );

      if (approvalLink) {
        window.location.href = approvalLink.href;
        await trackPurchase(amount, "USD", ["ColonFit"], title, savedEmail);
      } else {
        throw new Error("PayPal approval URL not found");
      }
    } catch (error) {
      console.error("Error creating PayPal order:", error);
      setError("Failed to initialize PayPal payment");
    } finally {
      setIsLoading(false);
    }
  };

  const handlePayPalButtonClick = () => {
    setSelectedPaymentMethod("paypal");
    if (isOneTimePayment) {
      handleOneTimePayPalPayment();
    } else {
      handlePayPalSubscription();
    }
  };

  useEffect(() => {
    const fetchClientSecret = async () => {
      if (
        selectedPaymentMethod === "card" &&
        !clientSecret &&
        !hasFetchedClientSecret &&
        PLAN_PRICE_IDS[plan]
      ) {
        try {
          setIsLoading(true);
          setHasFetchedClientSecret(true);

          let customerIdToUse = customerId;

          // If no customer ID is available yet, create one as fallback
          if (!customerIdToUse && savedEmail) {
            console.log(
              "🔄 Customer not found in Redux, creating fallback customer for:",
              savedEmail
            );
            const customerResponse = await fetch("/api/customer", {
              method: "POST",
              headers: { "Content-Type": "application/json" },
              body: JSON.stringify({ email: savedEmail }),
            });

            if (customerResponse.ok) {
              const { customer } = await customerResponse.json();
              customerIdToUse = customer.id;
              console.log("✅ Fallback customer created:", customerIdToUse);
            }
          } else if (customerIdToUse) {
            console.log(
              "✅ Using existing customer from Redux:",
              customerIdToUse
            );
          }

          if (!customerIdToUse) {
            setError("Unable to initialize payment. Please try again.");
            return;
          }

          const response = await fetch("/api/subscribe", {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({
              customerId: customerIdToUse,
              priceId: PLAN_PRICE_IDS[plan],
            }),
          });
          const data = await response.json();
          if (data.clientSecret) {
            setClientSecret(data.clientSecret);
            console.log("🔑 Client secret obtained successfully");
          } else {
            setError("Invalid client secret returned from server.");
          }
        } catch (err) {
          console.error("❌ Error fetching client secret:", err);
          setError("Error fetching client secret.");
        } finally {
          setIsLoading(false);
        }
      }
    };
    fetchClientSecret();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedPaymentMethod, plan, customerId, savedEmail]);

  const applyCoupon = () => {
    if (couponCode === "FIRSTBUY" && !isCouponApplied && isFirstSubscription) {
      const discount = amount * 0.1;
      setDiscountAmount(discount);
      setTotalAmount(amount - discount);
      setIsCouponApplied(true);
    }
  };

  if (error) {
    return <div className="text-red-500">{error}</div>;
  }

  if (isLoading) {
    return (
      <div className="flex justify-center items-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  return (
    <PayPalScriptProvider
      options={{ clientId: process.env.NEXT_PUBLIC_PAYPAL_CLIENT_ID! }}
    >
      <div className="w-full max-w-md mx-auto p-4 sm:p-6 md:p-8 bg-white rounded-lg shadow-lg overflow-x-hidden">
        <div className="mb-4 text-center">
          <h2 className="font-recoleta mb-2 text-xl">
            Select a payment method
          </h2>
          <Image
            src={cards}
            alt="Accepted cards"
            width={200}
            height={30}
            className="mx-auto mb-4"
          />
          <div className="space-y-3">
            <button
              onClick={() => setSelectedPaymentMethod("card")}
              className={`w-full flex items-center justify-center p-3 rounded-lg transition-colors duration-200 ${
                selectedPaymentMethod === "card"
                  ? "bg-black text-white"
                  : "bg-gray-100 text-black"
              }`}
            >
              <CreditCard className="mr-2" />
              Card
            </button>
          </div>
        </div>
        {selectedPaymentMethod === "card" && clientSecret && (
          <Elements
            stripe={stripePromise}
            options={{ clientSecret, appearance: { theme: "stripe" } }}
          >
            <CheckoutForm
              onSuccess={onSuccess}
              amount={price}
              title={plan}
              quantity={1}
              customerId={customerId}
            />
          </Elements>
        )}
        {selectedPaymentMethod === "card" && !clientSecret && (
          <div className="flex flex-col items-center justify-center py-8 space-y-4">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#184C43]"></div>
            <p className="text-[#184C43] text-sm">
              Preparing secure payment...
            </p>
          </div>
        )}
      </div>
    </PayPalScriptProvider>
  );
};

interface CheckoutFormProps {
  onSuccess: (transactionId: string) => void;
  amount: number;
  title: string;
  quantity: number;
  customerId: string | null;
}

function CheckoutForm({
  onSuccess,
  amount,
  title,
  quantity,
  customerId,
}: CheckoutFormProps) {
  const stripe = useStripe();
  const elements = useElements();
  const [message, setMessage] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const { trackPurchase } = useFacebookPixel();
  const shippingDetails = useAppSelector((state) => state.shipping);
  const [savedEmail] = useLocalStorage("userEmail", "");
  console.log("Customer ID in CheckoutForm:", customerId);
  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    if (!stripe || !elements) {
      setMessage("Unable to process payment. Please try again.");
      return;
    }

    setIsLoading(true);

    try {
      const updateResponse = await fetch("/api/update-customer", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          customerId,
          shippingDetails,
        }),
      });

      if (!updateResponse.ok) {
        throw new Error("Failed to update customer with shipping details");
      }
    } catch (error) {
      console.error("Error updating customer:", error);
      setMessage("There was an error processing your order. Please try again.");
      setIsLoading(false);
      return;
    }

    const { error, paymentIntent } = await stripe.confirmPayment({
      elements,
      confirmParams: {},
      redirect: "if_required",
    });

    if (error) {
      setMessage(error.message || "An unexpected error occurred.");
    } else if (paymentIntent && paymentIntent.status === "succeeded") {
      // Track purchase event with Facebook Pixel
      try {
        // Plan details mapping for tracking
        const planDetailsMap: Record<
          string,
          { contentIds: string[]; price: number }
        > = {
          "7day": { contentIds: ["LymphDrainage-7day"], price: 13.99 },
          "1month": { contentIds: ["LymphDrainage-1month"], price: 19.99 },
          "3month": { contentIds: ["LymphDrainage-3month"], price: 29.99 },
        };

        const planDetails = planDetailsMap[title] || planDetailsMap["3month"];
        await trackPurchase(
          amount,
          "USD",
          planDetails.contentIds,
          "subscription",
          savedEmail || shippingDetails.email
        );
        console.log("✅ Purchase event tracked successfully in PaymentForm");
      } catch (error) {
        console.error(
          "❌ Failed to track Purchase event in PaymentForm:",
          error
        );
      }

      // Call the onSuccess callback which will handle the redirect with proper tracking data
      onSuccess(paymentIntent.id);

      // Optionally, you can still try to send the email in the background, but do not block or show errors
      /*
      const emailData = {
        firstName: shippingDetails.firstName,
        email: shippingDetails.email,
        productName: title,
        shippingAddress: {
          streetAddress: shippingDetails.streetAddress,
          apartment: shippingDetails.apartment,
          city: shippingDetails.city,
          state: shippingDetails.state,
          zipCode: shippingDetails.zipCode,
          country: shippingDetails.country,
        },
        totalAmount: amount,
        quantity: quantity,
      };
      fetch("/api/send", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(emailData),
        });
      */
    }

    setIsLoading(false);
  };

  return (
    <form id="payment-form" onSubmit={handleSubmit} className="mt-4">
      <PaymentElement id="payment-element" className="mb-4" />
      <button
        disabled={isLoading || !stripe || !elements}
        id="submit"
        className="w-full bg-green-500 text-white py-3 px-6 rounded-full hover:bg-green-600 transition duration-300 text-lg font-semibold disabled:opacity-50 disabled:cursor-not-allowed"
      >
        <span id="button-text">
          {isLoading ? (
            <div className="spinner" id="spinner"></div>
          ) : (
            "Submit Secure Payment"
          )}
        </span>
      </button>
      {message && (
        <div id="payment-message" className="mt-4 text-red-500">
          {message}
        </div>
      )}
    </form>
  );
}

export default PaymentForm;
