import { useRouter } from 'next/navigation';
import React from 'react';


interface ShippingData {
    firstName: string;
    lastName: string;
    email: string;
    streetAddress: string;
    apartment?: string;
    city: string;
    state: string;
    zipCode: string;
    country: string;
}

interface ReceiptPageProps {
    amount: number;
    shippingData: ShippingData | null;
    transactionId: string;
}

const ReceiptPage: React.FC<ReceiptPageProps> = ({ amount, shippingData, transactionId }) => {
    const router = useRouter()

    const handleClick = () => {
        router.push('/')
    }

    return (
        <div className="text-center space-y-6">
            <h2 className="text-2xl font-recoleta text-[#008ab8]">Thank you for your order!</h2>
            <p className="text-lg">Your payment was successful.</p>
            <div className="bg-gray-100 p-6 rounded-lg">
                <p className="text-xl font-semibold mb-2">Order Summary</p>
                <p className="text-3xl font-bold text-[#008ab8] mb-4">${amount.toFixed(2)}</p>
                <p className="text-md mb-2"><strong>Transaction ID:</strong> {transactionId}</p>
                {shippingData && (
                    <div className="text-left">
                        <p><strong>Name:</strong> {shippingData.firstName} {shippingData.lastName}</p>
                        <p><strong>Email:</strong> {shippingData.email}</p>
                        <p><strong>Shipping Address:</strong></p>
                        <p>{shippingData.streetAddress}</p>
                        {shippingData.apartment && <p>{shippingData.apartment}</p>}
                        <p>{shippingData.city}, {shippingData.state} {shippingData.zipCode}</p>
                        <p>{shippingData.country}</p>
                    </div>
                )}
            </div>
            <p className="text-sm text-gray-600">
                An email confirmation has been sent to your registered email address.
                Please check your inbox (and spam folder) for order details.
            </p>
            <p className="text-sm text-gray-600">
                If you have any questions, please contact our support team at{' '}
                <a href="mailto:<EMAIL>" className="text-[#008ab8] hover:underline">
                    <EMAIL>
                </a>
            </p>
            <button
                className="bg-[#008ab8] text-white py-2 px-6 rounded-full hover:bg-[#006a8f] transition-colors duration-200 ease-in-out text-lg font-semibold"
                onClick={handleClick}
            >
                Continue Shopping
            </button>
        </div>
    );
};

export default ReceiptPage;