import React, { useEffect } from "react";
import { motion } from "framer-motion";
import PaymentForm from "./PaymentForm";
import { useCurrency } from '@/app/context/CurrencyContext';

interface CheckoutModalProps {
  isOpen: boolean;
  onClose: () => void;
  selectedPlan: string;
  totalUSD: number;
  totalDisplay: string;
  currency: string;
}

export default function CheckoutModal({
  isOpen,
  onClose,
  selectedPlan,
  totalUSD,
  totalDisplay,
  currency,
}: CheckoutModalProps) {
  const { convertPrice, formatPrice, isRateLimitHit } = useCurrency();

  // Store purchase details in localStorage when modal opens
  useEffect(() => {
    if (isOpen) {
      console.log("🛒 Storing purchase details in localStorage for tracking");
      localStorage.setItem("selectedPlan", selectedPlan);
      localStorage.setItem("purchaseAmount", totalUSD.toString());
      console.log("🚀 Modal opened - purchase tracking data stored");
    }
  }, [isOpen, selectedPlan, totalUSD]);

  // Format currency helper
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: currency || "USD",
    }).format(amount);
  };

  // Format plan name helper
  const formatPlanName = (plan: string) => {
    switch (plan) {
      case "7day":
        return "7-Day Program";
      case "1month":
        return "1-Month Program";
      case "3month":
        return "3-Month Program";
      default:
        return plan;
    }
  };

  // Get pricing details based on plan with proper currency conversion
  const getPricingDetails = (plan: string) => {
    const pricingData = {
      "7day": { 
        originalPriceUSD: 29.99, 
        discountedPriceUSD: 6.99, 
        days: 7 
      },
      "1month": { 
        originalPriceUSD: 44.75, 
        discountedPriceUSD: 19.99, 
        days: 30 
      },
      "3month": { 
        originalPriceUSD: 69.95, 
        discountedPriceUSD: 29.99, 
        days: 90 
      }
    };
    
    const data = pricingData[plan as keyof typeof pricingData] || pricingData["1month"];
    
    // Convert prices to local currency
    const originalPriceConverted = isRateLimitHit ? data.originalPriceUSD : convertPrice(data.originalPriceUSD);
    const discountedPriceConverted = isRateLimitHit ? data.discountedPriceUSD : convertPrice(data.discountedPriceUSD);
    const discountAmount = originalPriceConverted - discountedPriceConverted;
    
    // Calculate actual discount percentage based on converted prices
    const discountPercent = Math.round((discountAmount / originalPriceConverted) * 100);
    
    // Apply currency conversion
    return {
      originalPrice: isRateLimitHit ? `$${data.originalPriceUSD}` : formatPrice(originalPriceConverted),
      discountedPrice: isRateLimitHit ? `$${data.discountedPriceUSD}` : formatPrice(discountedPriceConverted),
      discount: isRateLimitHit ? `$${(data.originalPriceUSD - data.discountedPriceUSD).toFixed(2)}` : formatPrice(discountAmount),
      perDayPrice: isRateLimitHit ? `$${(data.discountedPriceUSD / data.days).toFixed(2)}` : formatPrice(discountedPriceConverted / data.days),
      discountPercent: discountPercent,
      days: data.days
    };
  };

  const pricingDetails = getPricingDetails(selectedPlan);

  // Handle successful payment with tracking data
  const handlePaymentSuccess = () => {
    console.log("💰 Payment successful - redirecting to thank you page");
    window.location.href = `/quiz/thank-you?plan=${selectedPlan}&amount=${totalUSD}&timestamp=${Date.now()}`;
  };

  return (
    <motion.div
      initial={{ y: "100%" }}
      animate={{ y: isOpen ? 0 : "100%" }}
      transition={{ type: "spring", damping: 25 }}
      className="fixed bottom-0 left-0 right-0 bg-white rounded-t-3xl shadow-2xl z-50 max-h-[90vh] flex justify-center items-end md:items-center md:py-8"
    >
      <div className="w-full max-w-md md:max-w-lg mx-auto text-black overflow-y-auto max-h-[90vh] pb-8 px-0">
        <div className="flex justify-between items-center mb-4 sticky top-0 bg-white pt-2 pb-2 z-10 px-4 md:px-8">
          <h2 className="text-xl font-semibold">Checkout</h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 text-2xl leading-none"
          >
            ×
          </button>
        </div>
        <div className="space-y-3 mb-1 px-4 md:px-8">
          {/* Original Price */}
          <div className="flex justify-between text-gray-600">
            <span>Personalized Plan</span>
            <span>{pricingDetails.originalPrice}</span>
          </div>
          
          {/* Discount */}
          <div className="flex justify-between text-red-500">
            <span>{pricingDetails.discountPercent}% Introductory discount</span>
            <span>-{pricingDetails.discount}</span>
          </div>
          
          {/* Per Day Price */}
          <div className="flex justify-between text-green-600 font-semibold">
            <span>Total per day:</span>
            <span>{pricingDetails.perDayPrice}</span>
          </div>
          
          {/* Final Total */}
          <div className="flex justify-between font-bold text-lg border-t pt-3">
            <span>Total:</span>
            <span>
              {pricingDetails.discountedPrice} for {pricingDetails.days === 7 ? '1 week' : pricingDetails.days === 30 ? '1 month' : '3 months'}
            </span>
          </div>
          
          {/* Savings Message */}
          <div className="text-center text-red-500 font-medium">
            You just saved {pricingDetails.discount} ({pricingDetails.discountPercent}% off)
          </div>
        </div>
        <div className="px-0 md:px-8">
          <PaymentForm
            plan={selectedPlan}
            price={totalUSD}
            onSuccess={handlePaymentSuccess}
          />
        </div>
        <div className="text-xs text-gray-400 text-center mt-8">
          All transactions are secure and encrypted
        </div>
      </div>
    </motion.div>
  );
}
