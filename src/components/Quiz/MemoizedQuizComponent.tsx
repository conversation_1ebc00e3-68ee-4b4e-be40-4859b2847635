import React, { memo, useCallback } from 'react';
import Image from 'next/image';
import { Option } from '@/types/quiztypes';

interface QuizComponentProps {
    question: string;
    options: Option[];
    type: 'single' | 'multiple';
    onNext: (selectedOptions: string[]) => void;
    onBack: () => void;
}

const MemoizedQuizComponent = memo(function QuizComponent({
    question,
    options,
    type,
    onNext,
    onBack,
}: QuizComponentProps) {
    const [selectedOptions, setSelectedOptions] = React.useState<string[]>([]);
    const [isExclusiveSelected, setIsExclusiveSelected] = React.useState(false);

    const handleOptionClick = useCallback((optionId: string) => {
        const clickedOption = options.find(opt => opt.id === optionId);

        if (type === 'single') {
            onNext([optionId]);
        } else {
            setSelectedOptions(prev => {
                if (clickedOption?.isExclusive) {
                    return prev.includes(optionId) ? [] : [optionId];
                } else {
                    const newSelection = prev.filter(id => !options.find(opt => opt.id === id)?.isExclusive);
                    if (newSelection.includes(optionId)) {
                        return newSelection.filter(id => id !== optionId);
                    } else {
                        return [...newSelection, optionId];
                    }
                }
            });
        }
    }, [options, type, onNext]);

    const handleContinue = useCallback(() => {
        if (selectedOptions.length > 0) {
            onNext(selectedOptions);
        }
    }, [selectedOptions, onNext]);

    const renderOption = useCallback((option: Option) => (
        <button
            key={option.id}
            onClick={() => handleOptionClick(option.id)}
            disabled={isExclusiveSelected && !option.isExclusive && !selectedOptions.includes(option.id)}
            className={`flex items-center p-4 rounded-lg transition-all duration-200 ease-in-out w-full
                ${selectedOptions.includes(option.id)
                    ? 'border-2 border-[#008ab8] bg-[#e6f3f7] shadow-md'
                    : 'bg-gray-100 border-2 border-transparent hover:border-[#008ab8] hover:bg-white'
                }
                ${isExclusiveSelected && !option.isExclusive && !selectedOptions.includes(option.id) ? 'opacity-50 cursor-not-allowed' : ''}
                md:flex-col md:justify-center md:h-32 md:w-32 text-left md:text-center text-sm`}
        >
            {option.image && (
                <Image
                    src={option.image}
                    alt={option.text}
                    width={24}
                    height={24}
                    className="mr-4 md:mr-0 md:mb-2 md:w-12 md:h-12"
                />
            )}
            <span className="text-black flex-grow md:flex-grow-0">{option.text}</span>
            {selectedOptions.includes(option.id) && (
                <span className="text-[#008ab8] ml-2 md:ml-0 md:mt-1">✓</span>
            )}
        </button>
    ), [handleOptionClick, isExclusiveSelected, selectedOptions]);

    return (
        <div className="w-full max-w-4xl mx-auto px-4 py-8 flex flex-col items-center relative">
            <h4 className="font-recoleta text-2xl md:text-4xl text-black mb-8 text-center">
                {question}
            </h4>
            <div className="font-recoleta flex flex-col md:flex-row md:flex-wrap md:justify-center gap-4 w-full mb-8">
                {options.map(renderOption)}
            </div>
            <div className="fixed bottom-0 left-0 right-0 flex justify-center mb-6 md:static md:mb-0">
                {type === 'multiple' && (
                    <button
                        onClick={handleContinue}
                        className="bg-[#008ab8] hover:bg-[#006a8f] text-white py-3 px-24 rounded-full transition-colors duration-200 ease-in-out text-lg font-semibold w-full max-w-xs"
                        disabled={selectedOptions.length === 0}
                    >
                        Continue
                    </button>
                )}
            </div>
            <button
                onClick={onBack}
                className="font-recoleta mt-4 text-[#008ab8] hover:text-[#006a8f] transition-colors duration-200 ease-in-out"
            >
                ← Go back
            </button>
        </div>
    );
});

export default MemoizedQuizComponent;