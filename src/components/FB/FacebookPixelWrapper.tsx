'use client';

import { useEffect } from 'react';
import useFacebookPixel from '../../hooks/useFacebookPixel';

interface FacebookPixelWrapperProps {
    children: React.ReactNode;
}

const FacebookPixelWrapper: React.FC<FacebookPixelWrapperProps> = ({ children }) => {
    const { trackPageView } = useFacebookPixel();

    useEffect(() => {
      
        trackPageView();
    }, [trackPageView]);

    return <>{children}</>;
};

export default FacebookPixelWrapper;