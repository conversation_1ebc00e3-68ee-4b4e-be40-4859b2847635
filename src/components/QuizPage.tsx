"use client";
import React from "react";
import { useRouter } from "next/navigation";
import { useLymphQuiz } from "@/app/quiz/LymphQuizContext";
import { QuizQuestion } from "@/data/quiz-data";
import { ArrowRight } from 'lucide-react';
import Image from "next/image";
import { motion, AnimatePresence } from "framer-motion";

interface QuizPageProps {
  question: QuizQuestion;
}

// Client-side interactive component
function QuizPageClient({ question }: QuizPageProps) {
  const { state, setAnswer } = useLymphQuiz();
  const selected = state.answers[question.id] || [];
  const router = useRouter();

  const isMultiple = !!question.allowMultiple;

  // Gender-based SVG logic for focus-areas
  const gender = state.answers["gender"]?.[0] || "female";
  function getFocusAreaSvg() {
    if (selected.length === 0) return `/images/focus-areas-${gender}-null.svg`;
    if (selected.length > 1) return `/images/focus-areas-${gender}-full.svg`;
    return `/images/focus-areas-${gender}-${selected[0]}.svg`;
  }

  const handleSelect = (id: string) => {
    if (isMultiple) {
      let newSelected;
      if (selected.includes(id)) {
        newSelected = selected.filter((sid: string) => sid !== id);
      } else {
        newSelected = [...selected, id];
      }
      setAnswer(question.id, newSelected);
    } else {
      setAnswer(question.id, [id]);
    }
  };

  const handleNext = () => {
    if (selected.length > 0) {
      if (question.id === "focus-areas") {
        // Check if only face-and-neck is selected
        const onlyFaceAndNeck = selected.length === 1 && selected[0] === "face-and-neck";
        // Check if only body parts are selected
        const onlyBodyParts = selected.every(option => 
          ["stomach", "legs", "glutes", "feet"].includes(option)
        );
        // Check if both face-and-neck and body parts are selected
        const bothSelected = selected.includes("face-and-neck") && 
          selected.some(option => ["stomach", "legs", "glutes", "feet"].includes(option));

        if (onlyFaceAndNeck) {
          router.push("/quiz/primary-goal");
        } else if (onlyBodyParts) {
          router.push("/quiz/swelling");
        } else if (bothSelected) {
          router.push("/quiz/dietary-habits");
        }
      } else if (question.id === "health-concerns") {
        // Check if "doctor" option is selected
        if (selected.includes("doctor")) {
          router.push("/quiz/trusted");
        } else {
          // For any other option, go directly to focus-areas
          router.push("/quiz/focus-areas");
        }
      } else {
        router.push(question.nextPage);
      }
    }
  };

  return (
    <div className="flex flex-col min-h-screen bg-white">
      {/* Green header with progress bar, question, subheading */}
      <div className="w-full bg-gradient-to-br from-[#0F403D] via-[#0E6661] to-[#0F403D] rounded-b-3xl px-4 pt-8 pb-10 md:pb-14 text-white relative overflow-hidden flex flex-col items-center">
        {/* Decorative icons */}
        <div className="absolute left-8 top-16 opacity-40 z-0">
          <svg width="32" height="32">
            <circle cx="16" cy="16" r="2" fill="#B6CFC7" />
            <line x1="16" y1="0" x2="16" y2="32" stroke="#B6CFC7" strokeWidth="1.5" />
            <line x1="0" y1="16" x2="32" y2="16" stroke="#B6CFC7" strokeWidth="1.5" />
          </svg>
        </div>
        <div className="absolute right-8 top-10 opacity-40 z-0">
          <svg width="32" height="32">
            <circle cx="16" cy="16" r="2" fill="#B6CFC7" />
            <line x1="16" y1="0" x2="16" y2="32" stroke="#B6CFC7" strokeWidth="1.5" />
            <line x1="0" y1="16" x2="32" y2="16" stroke="#B6CFC7" strokeWidth="1.5" />
          </svg>
        </div>
        {/* Progress bar */}
        <div className="w-full max-w-lg mx-auto mb-6">
          <div className="flex justify-between mb-2">
            <span className="text-sm text-white/70"> </span>
            <span className="text-sm text-white/80 font-medium">{question.progress}%</span>
          </div>
          <div className="h-2 bg-white/20 rounded-full overflow-hidden">
            <div className="h-full bg-[#c6e48b]" style={{ width: `${question.progress}%` }}></div>
          </div>
        </div>
        {/* Question and subheading */}
        <div className="w-full max-w-lg mx-auto text-center z-10">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-3 leading-tight">
            {question.title}
          </h2>
          <p className="text-white/70 text-base md:text-lg mb-2">
            {question.subheading}
          </p>
        </div>
      </div>
      {/* White section with answer options and Next button */}
      <div className="flex-1 w-full flex flex-col items-center bg-white px-4 pt-8 pb-16">
        {/* Gender-based SVG for focus-areas */}
        {question.id === "focus-areas" && (
          <div className="w-full flex justify-center mb-8">
            <div className="w-[340px] h-[340px] sm:w-[420px] sm:h-[420px] relative">
              <AnimatePresence mode="wait">
                <motion.img
                  key={getFocusAreaSvg()}
                  src={getFocusAreaSvg()}
                  alt="Focus Areas"
                  className="object-contain absolute inset-0 w-full h-full"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  exit={{ opacity: 0 }}
                  transition={{ duration: 0.3 }}
                />
              </AnimatePresence>
            </div>
          </div>
        )}
        <div className="w-full max-w-lg mx-auto space-y-4">
          {question.options?.map((opt) => (
            <button
              key={opt.id}
              type="button"
              className={`w-full py-4 px-6 rounded-full border text-lg font-medium flex items-center justify-start text-left transition-all
                ${selected.includes(opt.id)
                  ? 'bg-white border-[#184C43] text-[#184C43] font-semibold shadow'
                  : 'border-gray-200 text-gray-700 bg-white'}
                hover:border-[#184C43]'`}
              onClick={() => handleSelect(opt.id)}
            >
              {opt.icon && (
                <span className="mr-3 flex items-center justify-center shrink-0">
                  {opt.customIcon ? (
                    <Image
                      src={`/images/${opt.customIcon}.svg`}
                      alt={opt.text}
                      width={32}
                      height={32}
                      className="object-contain"
                      priority
                    />
                  ) : (
                    // Face/Skin Icon Placeholder
                    <svg width="18" height="18" fill="none" viewBox="0 0 18 18"><circle cx="9" cy="9" r="7" stroke="#184C43" strokeWidth="1.5"/><ellipse cx="9" cy="12" rx="4" ry="2" fill="#184C43" fillOpacity="0.15"/></svg>
                  )}
                </span>
              )}
              <span className="flex-1 text-left">{opt.text}</span>
              {isMultiple && (
                <span className={`ml-2 w-5 h-5 rounded-full border flex items-center justify-center ${selected.includes(opt.id) ? 'bg-[#184C43] border-[#184C43]' : 'border-gray-300'}`}>
                  {selected.includes(opt.id) && (
                    <svg width="14" height="14" viewBox="0 0 14 14" fill="none"><path d="M3 7l3 3 5-5" stroke="#fff" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/></svg>
                  )}
                </span>
              )}
            </button>
          )) ?? (
            <div className="text-center text-gray-500 py-4">
              No options available
            </div>
          )}
        </div>
        <button
          className={`mt-16 w-full max-w-lg py-4 px-6 rounded-full font-medium transition-all flex items-center justify-center text-lg
            ${selected.length > 0
              ? 'bg-[#c6e48b] text-[#184C43] hover:bg-[#b6d97a]'
              : 'bg-[#c6e48b]/50 text-[#184C43]/50 cursor-not-allowed'}`}
          disabled={selected.length === 0}
          onClick={handleNext}
        >
          Next <ArrowRight className="ml-2 h-5 w-5" />
        </button>
      </div>
    </div>
  );
}

// Server component that wraps the client component
export default function QuizPage(props: QuizPageProps) {
  return <QuizPageClient {...props} />;
} 
