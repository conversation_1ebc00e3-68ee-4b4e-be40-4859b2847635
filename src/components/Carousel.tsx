import React, { useState, useRef } from "react";
import Image from "next/image";
import { AnimatePresence, motion } from "framer-motion";

export interface Slide {
  imageUrl: string;
  alt: string;
  testimonial: string;
  name: string;
  location: string;
  year: string;
  rating: number;
}

interface CarouselProps {
  slides: Slide[];
}

const variants = {
  enter: (direction: number) => ({
    x: direction > 0 ? 300 : -300,
    opacity: 0,
    position: "absolute" as const,
  }),
  center: {
    x: 0,
    opacity: 1,
    position: "relative" as const,
  },
  exit: (direction: number) => ({
    x: direction < 0 ? 300 : -300,
    opacity: 0,
    position: "absolute" as const,
  }),
};

const Carousel: React.FC<CarouselProps> = ({ slides }) => {
  const [[current, direction], setCurrent] = useState<[number, number]>([0, 0]);
  const [isAnimating, setIsAnimating] = useState(false);
  const slideCount = slides.length;
  const carouselRef = useRef<HTMLDivElement>(null);

  const goTo = (idx: number) => {
    if (isAnimating) return;
    const newIndex = (idx + slideCount) % slideCount;
    setCurrent([newIndex, idx > current ? 1 : -1]);
    setIsAnimating(true);
  };

  // Keyboard navigation
  React.useEffect(() => {
    const handleKey = (e: KeyboardEvent) => {
      if (e.key === "ArrowLeft") goTo(current - 1);
      if (e.key === "ArrowRight") goTo(current + 1);
    };
    window.addEventListener("keydown", handleKey);
    return () => window.removeEventListener("keydown", handleKey);
  }, [current, slideCount]);

  // Touch navigation
  let startX = 0;
  const handleTouchStart = (e: React.TouchEvent) => {
    startX = e.touches[0].clientX;
  };
  const handleTouchEnd = (e: React.TouchEvent) => {
    const endX = e.changedTouches[0].clientX;
    if (startX - endX > 50) goTo(current + 1);
    if (endX - startX > 50) goTo(current - 1);
  };

  return (
    <div
      className="relative w-full max-w-2xl mx-auto"
      ref={carouselRef}
      aria-roledescription="carousel"
      aria-label="Testimonials"
      tabIndex={0}
      onTouchStart={handleTouchStart}
      onTouchEnd={handleTouchEnd}
    >
      {/* Slides */}
      <div className="overflow-hidden rounded-xl w-full">
        <AnimatePresence
          initial={false}
          custom={direction}
          onExitComplete={() => setIsAnimating(false)}
        >
          <motion.div
            key={current}
            custom={direction}
            variants={variants}
            initial="enter"
            animate="center"
            exit="exit"
            transition={{ type: "spring", stiffness: 300, damping: 30 }}
            className="w-full h-full flex flex-col items-center"
          >
            <div className="w-full aspect-[4/3] sm:aspect-[16/9] relative">
              <Image
                src={slides[current].imageUrl}
                alt={slides[current].alt}
                fill
                className="object-contain rounded-t-xl"
                priority
                sizes="(max-width: 640px) 100vw, 600px"
              />
            </div>
            <div className="w-full max-w-2xl p-6 bg-white/90 rounded-b-xl shadow-md">
              <p className="font-bold text-lg text-gray-900">{slides[current].name}</p>
              <p className="text-sm text-gray-500 mb-2">{slides[current].location}, {slides[current].year}</p>
              <p className="mt-2 text-base text-gray-800 leading-relaxed break-words whitespace-pre-line">{slides[current].testimonial}</p>
              <div className="mt-2 text-yellow-400 text-lg">
                {"★".repeat(slides[current].rating)}{"☆".repeat(5 - slides[current].rating)}
              </div>
            </div>
          </motion.div>
        </AnimatePresence>
      </div>
      {/* Navigation */}
      <button
        className="absolute left-2 top-1/2 -translate-y-1/2 bg-white/80 rounded-full p-2 shadow"
        onClick={() => goTo(current - 1)}
        aria-label="Previous Slide"
        disabled={isAnimating}
      >
        &#8592;
      </button>
      <button
        className="absolute right-2 top-1/2 -translate-y-1/2 bg-white/80 rounded-full p-2 shadow"
        onClick={() => goTo(current + 1)}
        aria-label="Next Slide"
        disabled={isAnimating}
      >
        &#8594;
      </button>
      {/* Dots */}
      <div className="flex justify-center mt-2 gap-2">
        {slides.map((_, idx) => (
          <button
            key={idx}
            className={`w-2 h-2 rounded-full ${idx === current ? "bg-green-600" : "bg-gray-300"}`}
            onClick={() => goTo(idx)}
            aria-label={`Go to slide ${idx + 1}`}
            disabled={isAnimating}
          />
        ))}
      </div>
    </div>
  );
};

export default Carousel; 