"use client";

import React, { useState } from "react";
import { useRouter } from "next/navigation";
import { QuizQuestion } from "@/data/quiz-data";
import Image from "next/image";

interface FocusAreasPageProps {
  question: QuizQuestion;
}

export default function FocusAreasPage({ question }: FocusAreasPageProps) {
  const [selected, setSelected] = useState<string[]>([]);
  const router = useRouter();

  const handleSelect = (id: string) => {
    if (selected.includes(id)) {
      setSelected(selected.filter(item => item !== id));
    } else {
      setSelected([...selected, id]);
    }
  };

  const handleNext = () => {
    if (selected.length > 0) {
      router.push(question.nextPage);
    }
  };

  return (
    <div className="flex flex-col min-h-screen bg-white">
      {/* Green header with progress bar, question, subheading */}
      <div className="w-full bg-gradient-to-br from-[#184C43] to-[#256d5a] rounded-b-3xl px-4 pt-8 pb-10 md:pb-14 text-white relative overflow-hidden flex flex-col items-center">
        {/* Decorative icons */}
        <div className="absolute left-8 top-16 opacity-40 z-0">
          <svg width="32" height="32">
            <circle cx="16" cy="16" r="2" fill="#B6CFC7" />
            <line x1="16" y1="0" x2="16" y2="32" stroke="#B6CFC7" strokeWidth="1.5" />
            <line x1="0" y1="16" x2="32" y2="16" stroke="#B6CFC7" strokeWidth="1.5" />
          </svg>
        </div>
        <div className="absolute right-8 top-10 opacity-40 z-0">
          <svg width="32" height="32">
            <circle cx="16" cy="16" r="2" fill="#B6CFC7" />
            <line x1="16" y1="0" x2="16" y2="32" stroke="#B6CFC7" strokeWidth="1.5" />
            <line x1="0" y1="16" x2="32" y2="16" stroke="#B6CFC7" strokeWidth="1.5" />
          </svg>
        </div>
        {/* Progress bar */}
        <div className="w-full max-w-lg mx-auto mb-6">
          <div className="flex justify-between mb-2">
            <span className="text-sm text-white/70"> </span>
            <span className="text-sm text-white/80 font-medium">{question.progress}%</span>
          </div>
          <div className="h-2 bg-white/20 rounded-full overflow-hidden">
            <div className="h-full bg-[#c6e48b]" style={{ width: `${question.progress}%` }}></div>
          </div>
        </div>
        {/* Question and subheading */}
        <div className="w-full max-w-lg mx-auto text-center z-10">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-3 leading-tight">
            {question.title}
          </h2>
          <p className="text-white/70 text-base md:text-lg mb-2">
            {question.subheading}
          </p>
        </div>
      </div>
      {/* White section with image, options, and Next button */}
      <div className="flex-1 w-full flex flex-col items-center bg-white px-4 pt-8 pb-16">
        {/* Image */}
        {question.imageUrl && (
          <div className="w-full flex justify-center mb-8">
            <div className="w-[340px] h-[340px] sm:w-[420px] sm:h-[420px] relative">
              <Image
                src={question.imageUrl}
                alt="Focus Areas"
                fill
                className="object-contain"
                priority
              />
            </div>
          </div>
        )}
        {/* Options as pill buttons */}
        <div className="flex flex-col items-end mb-8">
          {question.options?.map((opt) => (
            <button
              key={opt.id}
              className={`mb-2 px-6 py-2 rounded-full border text-center transition-all text-base font-medium min-w-[120px] max-w-xs
                ${selected.includes(opt.id)
                  ? "bg-white border-[#184C43] text-[#184C43] font-semibold shadow"
                  : "border-gray-200 text-gray-700 bg-white"}
                hover:border-[#184C43]`}
              onClick={() => handleSelect(opt.id)}
            >
              {opt.text}
            </button>
          ))}
        </div>
        {/* Next button */}
        <button
          className={`mt-8 w-full max-w-lg py-4 px-6 rounded-full font-medium transition-all flex items-center justify-center text-lg
            ${selected.length > 0
              ? "bg-[#c6e48b] text-[#184C43] hover:bg-[#b6d97a]"
              : "bg-[#c6e48b]/50 text-[#184C43]/50 cursor-not-allowed"}`}
          disabled={selected.length === 0}
          onClick={handleNext}
        >
          Next
        </button>
      </div>
    </div>
  );
} 