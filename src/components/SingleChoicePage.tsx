"use client";

import React, { useState } from "react";
import { useRouter } from "next/navigation";
import { QuizQuestion } from "@/data/quiz-data";
import { useLymphQuiz } from "@/app/quiz/LymphQuizContext";

interface SingleChoicePageProps {
  question: QuizQuestion;
}

export default function SingleChoicePage({ question }: SingleChoicePageProps) {
  const { state, setAnswer } = useLymphQuiz();
  const [selected, setSelected] = useState<string | null>(state.answers[question.id]?.[0] || null);
  const router = useRouter();

  const handleSelect = (id: string) => {
    console.log('SingleChoicePage selecting answer:', question.id, [id]);
    setSelected(id);
    setAnswer(question.id, [id]);
  };

  const handleNext = () => {
    if (selected) {
      router.push(question.nextPage);
    }
  };

  const renderCustomIcon = (icon: string) => {
    switch (icon) {
      case "leg":
        return "🦵";
      case "arm":
        return "💪";
      case "face":
        return "😊";
      case "body":
        return "👤";
      case "sunmoon":
        return (
          <span className="inline-flex items-center">
            <svg width="16" height="16" fill="none" viewBox="0 0 16 16" className="mr-1">
              <circle cx="5" cy="8" r="3" fill="#184C43"/>
              <circle cx="11" cy="8" r="3" fill="#B6CFC7"/>
            </svg>
          </span>
        );
      case "sun":
        return (
          <svg width="16" height="16" fill="none" viewBox="0 0 16 16">
            <circle cx="8" cy="8" r="3" fill="#FFD700" stroke="#184C43" strokeWidth="1.2"/>
          </svg>
        );
      case "moon":
        return (
          <svg width="16" height="16" fill="none" viewBox="0 0 16 16">
            <path d="M12 8a4 4 0 1 1-4-4c0 2.21 1.79 4 4 4z" fill="#B6CFC7" stroke="#184C43" strokeWidth="1.2"/>
          </svg>
        );
      case "neutral":
        return (
          <svg width="16" height="16" fill="none" viewBox="0 0 16 16">
            <circle cx="8" cy="8" r="7" stroke="#184C43" strokeWidth="1.2"/>
            <rect x="5" y="11" width="6" height="1" rx="0.5" fill="#184C43"/>
          </svg>
        );
      case "cleanser":
        return (
          <svg width="16" height="16" fill="none" viewBox="0 0 16 16">
            <rect x="5" y="3" width="6" height="10" rx="2" fill="#184C43"/>
          </svg>
        );
      case "moisturizer":
        return (
          <svg width="16" height="16" fill="none" viewBox="0 0 16 16">
            <ellipse cx="8" cy="10" rx="4" ry="3" fill="#B6CFC7"/>
            <rect x="6" y="3" width="4" height="4" rx="2" fill="#184C43"/>
          </svg>
        );
      case "sunscreen":
        return (
          <svg width="16" height="16" fill="none" viewBox="0 0 16 16">
            <circle cx="8" cy="8" r="3" fill="#FFD700" stroke="#184C43" strokeWidth="1.2"/>
          </svg>
        );
      case "exfoliator":
        return (
          <svg width="16" height="16" fill="none" viewBox="0 0 16 16">
            <rect x="4" y="4" width="8" height="8" rx="2" fill="#B6CFC7"/>
            <rect x="7" y="7" width="2" height="2" rx="1" fill="#184C43"/>
          </svg>
        );
      case "toner":
        return (
          <svg width="16" height="16" fill="none" viewBox="0 0 16 16">
            <rect x="6" y="2" width="4" height="12" rx="2" fill="#184C43"/>
          </svg>
        );
      case "serum":
        return (
          <svg width="16" height="16" fill="none" viewBox="0 0 16 16">
            <rect x="7" y="4" width="2" height="8" rx="1" fill="#B6CFC7"/>
            <circle cx="8" cy="12" r="2" fill="#184C43"/>
          </svg>
        );
      case "eye":
        return (
          <svg width="16" height="16" fill="none" viewBox="0 0 16 16">
            <ellipse cx="8" cy="8" rx="6" ry="3" fill="#B6CFC7"/>
            <circle cx="8" cy="8" r="1.5" fill="#184C43"/>
          </svg>
        );
      case "mask":
        return (
          <svg width="16" height="16" fill="none" viewBox="0 0 16 16">
            <ellipse cx="8" cy="10" rx="4" ry="3" fill="#B6CFC7"/>
            <rect x="6" y="3" width="4" height="4" rx="2" fill="#184C43"/>
          </svg>
        );
      case "makeup":
        return (
          <svg width="16" height="16" fill="none" viewBox="0 0 16 16">
            <rect x="7" y="2" width="2" height="8" rx="1" fill="#B6CFC7"/>
            <ellipse cx="8" cy="12" rx="4" ry="2" fill="#184C43"/>
          </svg>
        );
      default:
        return "🦵";
    }
  };

  return (
    <div className="flex flex-col min-h-screen bg-white">
      {/* Green header with progress bar, question, subheading */}
      <div className="w-full bg-gradient-to-br from-[#0F403D] via-[#0E6661] to-[#0F403D] rounded-b-3xl px-4 pt-8 pb-10 md:pb-14 text-white relative overflow-hidden flex flex-col items-center">
        {/* Decorative icons */}
        <div className="absolute left-8 top-16 opacity-40 z-0">
          <svg width="32" height="32">
            <circle cx="16" cy="16" r="2" fill="#B6CFC7" />
            <line x1="16" y1="0" x2="16" y2="32" stroke="#B6CFC7" strokeWidth="1.5" />
            <line x1="0" y1="16" x2="32" y2="16" stroke="#B6CFC7" strokeWidth="1.5" />
          </svg>
        </div>
        <div className="absolute right-8 top-10 opacity-40 z-0">
          <svg width="32" height="32">
            <circle cx="16" cy="16" r="2" fill="#B6CFC7" />
            <line x1="16" y1="0" x2="16" y2="32" stroke="#B6CFC7" strokeWidth="1.5" />
            <line x1="0" y1="16" x2="32" y2="16" stroke="#B6CFC7" strokeWidth="1.5" />
          </svg>
        </div>
        {/* Progress bar */}
        <div className="w-full max-w-lg mx-auto mb-6">
          <div className="flex justify-between mb-2">
            <span className="text-sm text-white/70"> </span>
            <span className="text-sm text-white/80 font-medium">{question.progress}%</span>
          </div>
          <div className="h-2 bg-white/20 rounded-full overflow-hidden">
            <div className="h-full bg-[#c6e48b]" style={{ width: `${question.progress}%` }}></div>
          </div>
        </div>
        {/* Question and subheading */}
        <div className="w-full max-w-lg mx-auto text-center z-10">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-3 leading-tight">
            {question.title}
          </h2>
          <p className="text-white/70 text-base md:text-lg mb-2">
            {question.subheading}
          </p>
        </div>
      </div>
      {/* White section with answer options and Next button */}
      <div className="flex-1 w-full flex flex-col items-center bg-white px-4 pt-8 pb-16">
        <div className="w-full max-w-lg mx-auto space-y-4">
          {question.options?.map((opt) => (
            <button
              key={opt.id}
              className={`w-full py-4 px-6 rounded-full border text-center transition-all text-lg font-medium flex items-center justify-center
                ${selected === opt.id
                  ? 'bg-white border-[#184C43] text-[#184C43] font-semibold shadow'
                  : 'border-gray-200 text-gray-700 bg-white'}
                hover:border-[#184C43]'`}
              onClick={() => handleSelect(opt.id)}
            >
              {opt.icon && (
                <span className="mr-2">
                  {opt.customIcon ? renderCustomIcon(opt.customIcon) : "🦵"}
                </span>
              )}
              {opt.text}
            </button>
          ))}
        </div>
        <button
          className={`mt-16 w-full max-w-lg py-4 px-6 rounded-full font-medium transition-all flex items-center justify-center text-lg
            ${selected
              ? 'bg-[#c6e48b] text-[#184C43] hover:bg-[#b6d97a]'
              : 'bg-[#c6e48b]/50 text-[#184C43]/50 cursor-not-allowed'}`}
          disabled={!selected}
          onClick={handleNext}
        >
          Next
        </button>
      </div>
    </div>
  );
} 
