import React, { useState } from 'react';
import Image from 'next/image';
import cards from '../../../public/cards2.png';
import { ItemCard } from './ItemCard';
import freegifts from '../../../public/customsvg/gift-svgrepo-com.svg';
import freeshipping from '../../../public/customsvg/truck-svgrepo-com.svg';
import madeingermany from '../../../public/customsvg/factory-industry-manufacturing-production-svgrepo-com.svg';

interface BundleSectionProps {
    planContent: any;
    onSelectPlan: (price: number, paymentType: string, priceId: string, title: string) => void;
}

export const BundleSection: React.FC<BundleSectionProps> = ({ planContent, onSelectPlan }) => {
    const [paymentType, setPaymentType] = useState<'ONE-TIME-PAYMENT' | 'SUBSCRIPTION'>('ONE-TIME-PAYMENT');

    const orderedBundles = ['6-month', '3-month', '1-month'];

    return (
        <div className="container mx-auto px-4 py-8">
            <h2 className="text-3xl font-recoleta mb-6 text-center text-black">Choose your bundle</h2>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
                {orderedBundles.map((key, index) => {
                    const value = planContent[key];
                    return (
                        <ItemCard
                            key={key}
                            {...value[paymentType === 'SUBSCRIPTION' ? 1 : 0]}
                            paymentType={paymentType}
                            setPaymentType={setPaymentType}
                            onSelect={() => onSelectPlan(
                                parseFloat(value[paymentType === 'SUBSCRIPTION' ? 1 : 0].price),
                                paymentType,
                                value[paymentType === 'SUBSCRIPTION' ? 1 : 0].priceId,
                                value[paymentType === 'SUBSCRIPTION' ? 1 : 0].title
                            )}
                            tag={index === 0 ? 'BEST VALUE' : index === 1 ? 'MOST POPULAR' : ''}
                        />
                    );
                })}
            </div>

            <div className="flex justify-center space-x-8 mb-8">
                <div className="flex flex-col items-center">
                    <Image src={freeshipping} alt="Free Shipping" width={80} height={80} />
                    <p className="mt-2 text-sm">Free Shipping</p>
                </div>
                <div className="flex flex-col items-center">
                    <Image src={madeingermany} alt="Products Made in Germany" width={80} height={80} />
                    <p className="mt-2 text-sm">Products Made in Germany</p>
                </div>
                <div className="flex flex-col items-center">
                    <Image src={freegifts} alt="Free Gift Included" width={80} height={80} />
                    <p className="mt-2 text-sm">Free Gift Included</p>
                </div>
            </div>

            <div className="flex justify-center">
                <Image src={cards} alt="Payment Methods" width={300} height={50} />
            </div>
        </div>
    );
};