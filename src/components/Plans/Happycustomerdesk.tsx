import React, { useState, useEffect } from 'react';
import { Star, CheckCircle, ChevronRight, ChevronLeft } from 'lucide-react';
import { feedbackData } from '@/data/feedbackData';

interface FeedbackCardProps {
    rating: number;
    heading: string;
    description: string;
    name: string;
}

const FeedbackCard: React.FC<FeedbackCardProps> = ({ rating, heading, description, name }) => {
    return (
        <div className="bg-gray-100 rounded-lg p-4 text-left w-full h-full flex flex-col justify-between">
            <div>
                <div className="flex mb-2">
                    {[...Array(5)].map((_, index) => (
                        <Star
                            key={index}
                            size={16}
                            className={`w-4 h-4 ${index < rating ? 'text-yellow-400 fill-current' : 'text-gray-300'}`}
                        />
                    ))}
                </div>
                <h2 className="font-bold text-base mb-2">{heading}</h2>
                <p className="text-gray-600 text-sm">{description}</p>
            </div>
            <div className="flex items-center mt-3">
                <span className="font-semibold text-sm mr-2">{name}</span>
                <CheckCircle size={12} className="text-green-500 w-3 h-3" />
                <span className="text-xs text-green-500 ml-1">Verified Customer</span>
            </div>
        </div>
    )
}

const HappyCustomersdesk: React.FC = () => {
    const [currentIndex, setCurrentIndex] = useState(0);
    const [slidesToShow, setSlidesToShow] = useState(1);

    useEffect(() => {
        const handleResize = () => {
            if (window.innerWidth >= 1024) {
                setSlidesToShow(3);
            } else if (window.innerWidth >= 768) {
                setSlidesToShow(2);
            } else {
                setSlidesToShow(1);
            }
        };

        handleResize();
        window.addEventListener('resize', handleResize);
        return () => window.removeEventListener('resize', handleResize);
    }, []);

    const nextSlide = () => {
        setCurrentIndex((prevIndex) => (prevIndex + 1) % feedbackData.length);
    };

    const prevSlide = () => {
        setCurrentIndex((prevIndex) => (prevIndex - 1 + feedbackData.length) % feedbackData.length);
    };

    return (
        <div className="max-w-6xl mx-auto px-4 py-8">
            <h1 className="text-2xl md:text-3xl font-recoleta text-center mb-2">Thousands of Happy Clients</h1>
            <p className="text-center mb-6 text-sm md:text-base">This could be you - check out these reviews from happy poopers around the world!</p>
            <div className="relative">
                <div className="overflow-hidden">
                    <div
                        className="flex transition-transform duration-300 ease-in-out"
                        style={{ transform: `translateX(-${currentIndex * (100 / slidesToShow)}%)` }}
                    >
                        {feedbackData.map((feedback, index) => (
                            <div key={index} className={`flex-shrink-0 w-full md:w-1/2 lg:w-1/3 px-2`}>
                                <FeedbackCard {...feedback} />
                            </div>
                        ))}
                    </div>
                </div>
                <button
                    onClick={prevSlide}
                    className="absolute left-0 top-1/2 transform -translate-y-1/2 bg-white rounded-full p-1 shadow-md"
                >
                    <ChevronLeft size={20} className="text-green-500" />
                </button>
                <button
                    onClick={nextSlide}
                    className="absolute right-0 top-1/2 transform -translate-y-1/2 bg-white rounded-full p-1 shadow-md"
                >
                    <ChevronRight size={20} className="text-green-500" />
                </button>
            </div>
        </div>
    );
}

export default HappyCustomersdesk;