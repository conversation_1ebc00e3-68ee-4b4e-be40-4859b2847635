import React, { useState} from 'react';
import { Star, CheckCircle, ChevronRight, ChevronLeft } from 'lucide-react';
import { feedbackData } from '@/data/feedbackData';

interface FeedbackCardProps {
    rating: number;
    heading: string;
    description: string;
    name: string;
}

const FeedbackCard: React.FC<FeedbackCardProps> = ({ rating, heading, description, name }) => {
    return (
        <div className="bg-gray-100 rounded-lg p-4 text-left w-full">
            <div className="flex mb-2">
                {[...Array(5)].map((_, index) => (
                    <Star
                        key={index}
                        size={16}
                        className={`w-4 h-4 ${index < rating ? 'text-yellow-400 fill-current' : 'text-gray-300'}`}
                    />
                ))}
            </div>
            <h2 className="font-bold text-base mb-2">{heading}</h2>
            <p className="text-gray-600 text-sm mb-3">{description}</p>
            <div className="flex items-center">
                <span className="font-semibold text-sm mr-2">{name}</span>
                <CheckCircle size={12} className="text-green-500 w-3 h-3" />
                <span className="text-xs text-green-500 ml-1">Verified Customer</span>
            </div>
        </div>
    )
}

const HappyCustomersmob: React.FC = () => {
    const [currentIndex, setCurrentIndex] = useState(0);

    const nextSlide = () => {
        setCurrentIndex((prevIndex) => (prevIndex + 1) % feedbackData.length);
    };

    const prevSlide = () => {
        setCurrentIndex((prevIndex) => (prevIndex - 1 + feedbackData.length) % feedbackData.length);
    };

    return (
        <div className="max-w-md mx-auto px-4 py-8">
            <h1 className="text-2xl font-recoleta text-center mb-2">Thousands of Happy Clients</h1>
            <p className="text-center mb-6 text-sm">This could be you - check out these reviews from happy poopers around the world!</p>
            <div className="relative">
                <FeedbackCard {...feedbackData[currentIndex]} />
                <button
                    onClick={prevSlide}
                    className="absolute left-0 top-1/2 transform -translate-y-1/2 bg-white rounded-full p-1 shadow-md"
                >
                    <ChevronLeft size={20} className="text-green-500" />
                </button>
                <button
                    onClick={nextSlide}
                    className="absolute right-0 top-1/2 transform -translate-y-1/2 bg-white rounded-full p-1 shadow-md"
                >
                    <ChevronRight size={20} className="text-green-500" />
                </button>
            </div>
        </div>
    );
}

export default HappyCustomersmob;