import React from 'react';
import Image, { StaticImageData } from 'next/image';
import { Check } from 'lucide-react';

interface ItemCardProps {
    title: string;
    image: StaticImageData;
    price: string;
    servings: string;
    delivery: string;
    onSelect: () => void;
    paymentType: 'ONE-TIME-PAYMENT' | 'SUBSCRIPTION';
    setPaymentType: (type: 'ONE-TIME-PAYMENT' | 'SUBSCRIPTION') => void;
    tag?: string;
}

export const ItemCard: React.FC<ItemCardProps> = ({
    title,
    image,
    price,
    servings,
    delivery,
    onSelect,
    paymentType,
    setPaymentType,
    tag
}) => {
    return (
        <div className="bg-white rounded-lg shadow-lg overflow-hidden relative flex flex-col h-full">
            {tag && (
                <div className={`absolute top-0 left-0 ${tag === 'BEST VALUE' ? 'bg-yellow-400' : 'bg-purple-400'} text-white text-xs font-bold px-2 py-1`}>
                    {tag}
                </div>
            )}
            <div className="p-6 flex flex-col flex-grow">
                <h3 className="text-xl font-recoleta mb-4 text-center">{title}</h3>
                <div className="flex-grow flex flex-col justify-between">
                    <div>
                        <div className="h-48 flex items-center justify-center mb-6">
                            <Image
                                src={image}
                                alt="Product"
                                width={180}
                                height={180}
                                className="max-h-full w-auto object-contain"
                            />
                        </div>
                        <div className="text-center mb-6">
                            <span className="text-4xl font-recoleta text-black">${price}</span>
                            <span className="text-xl font-recoleta text-gray-600 ml-2">per bottle</span>
                        </div>
                        <ul className="space-y-3 mb-6">
                            <li className="flex items-center">
                                <Check className="w-5 h-5 mr-2 text-[#008ab1]" />
                                <span className="text-gray-700">{servings}</span>
                            </li>
                            <li className="flex items-center">
                                <Check className="w-5 h-5 mr-2 text-[#008ab1]" />
                                <span className="text-gray-700">{delivery}</span>
                            </li>
                        </ul>
                    </div>
                    <div className="mt-auto">
                        <div className="flex flex-col space-y-2 mb-4">
                            <label className="inline-flex items-center">
                                <input
                                    type="radio"
                                    checked={paymentType === 'ONE-TIME-PAYMENT'}
                                    onChange={() => setPaymentType('ONE-TIME-PAYMENT')}
                                    className="form-radio h-5 w-5 text-[#008ab1]"
                                />
                                <span className="ml-2 text-sm">One-time purchase</span>
                            </label>
                            <label className="inline-flex items-center">
                                <input
                                    type="radio"
                                    checked={paymentType === 'SUBSCRIPTION'}
                                    onChange={() => setPaymentType('SUBSCRIPTION')}
                                    className="form-radio h-5 w-5 text-[#008ab1]"
                                />
                                <span className="ml-2 text-sm">Subscribe & Save <span className=' text-xs italic'>*Extra discounts on first purchase</span></span>
                            </label>
                        </div>
                        <button
                            className="w-full bg-[#008ab1] text-white font-bold py-3 px-4 rounded-lg hover:bg-[#006d8f] transition duration-300 text-lg"
                            onClick={onSelect}
                        >
                            {paymentType === 'SUBSCRIPTION' ? "Subscribe Now" : "Buy Now"}
                        </button>

                        <p className="text-sm text-gray-500 mt-2 text-center">BACK IN SHAPE SALE - 35% OFF </p>

                    </div>
                </div>
            </div>
        </div>
    );
};