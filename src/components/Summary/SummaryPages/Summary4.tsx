'use client'
import React, { useState, useEffect } from 'react';
import { ChevronLeft, ChevronRight, Star } from 'lucide-react';
import featuredin from '../../../../public/featured_plans.png';
import Image from 'next/image';

interface FeedbackItem {
    rating: number;
    heading: string;
    description: string;
    name: string;
    imagePath: string;
}

interface PreloadedImage {
    src: string;
    alt: string;
}

const feedbackData: FeedbackItem[] = [
    { rating: 5, heading: "Helps with regularity", description: "For the first time as an adult, I'm regular! Less bloating and daily bowel movements. This product tastes great and works wonders.", name: "<PERSON>", imagePath: "/photoreviews/CF REF1.png" },
    { rating: 4, heading: "Great Product", description: "I've noticed a significant improvement in my energy levels. Highly recommended!", name: "<PERSON>", imagePath: "/photoreviews/CF REF2.png" },
    { rating: 5, heading: "Life-changing", description: "This product has completely transformed my daily routine. I feel amazing!", name: "<PERSON>", imagePath: "/photoreviews/CF REF3.png" },
    { rating: 4, heading: "Very Satisfied", description: "I am very satisfied with the results so far. It delivers what it promises.", name: "<PERSON>.", imagePath: "/photoreviews/CF REF4.png" },
    { rating: 5, heading: "Fantastic!", description: "Fantastic product! My digestion has improved significantly.", name: "Isabella B.", imagePath: "/photoreviews/CF REF5.png" },
    { rating: 5, heading: "Amazing Results", description: "I've struggled with digestion for years, but this product has been a game changer for me!", name: "Laura H.", imagePath: "/photoreviews/CF REF6.png" },
    { rating: 4, heading: "Good Overall", description: "The product works well, but I wish it came in more flavors. Still a great addition to my routine.", name: "Kevin P.", imagePath: "/photoreviews/CF REF7.png" },
    { rating: 5, heading: "Highly Effective", description: "This has quickly become a staple in my diet. I feel healthier and more energetic every day.", name: "Sophia W.", imagePath: "/photoreviews/CF REF8.png" },
    { rating: 4, heading: "Impressive", description: "I've seen noticeable improvements in my overall wellness. Just wish it was a bit more affordable.", name: "Mia L.", imagePath: "/photoreviews/CF REF9.png" },
];

const FeedbackCarousel: React.FC = () => {
    const [currentIndex, setCurrentIndex] = useState<number>(0);
    const [imagesLoaded, setImagesLoaded] = useState<boolean>(false);
    const [preloadedImages, setPreloadedImages] = useState<PreloadedImage[]>([]);

    useEffect(() => {
        const loadImages = async () => {
            try {
                const images: PreloadedImage[] = feedbackData.map((item) => ({
                    src: item.imagePath,
                    alt: `${item.name}'s profile`
                }));

                await Promise.all(
                    images.map((image) => {
                        return new Promise<void>((resolve, reject) => {
                            const img = new window.Image();
                            img.src = image.src;
                            img.onload = () => resolve();
                            img.onerror = reject;
                        });
                    })
                );

                setPreloadedImages(images);
                setImagesLoaded(true);
            } catch (error) {
                console.error('Error preloading images:', error);
                setImagesLoaded(true);
            }
        };

        loadImages();
    }, []);

    const nextSlide = () => {
        setCurrentIndex((prevIndex) => (prevIndex + 1) % feedbackData.length);
    };

    const prevSlide = () => {
        setCurrentIndex((prevIndex) => (prevIndex - 1 + feedbackData.length) % feedbackData.length);
    };

    if (!imagesLoaded) {
        return (
            <div className="w-full max-w-4xl mx-auto bg-white rounded-lg shadow-xl p-6">
                <div className="animate-pulse">
                    <div className="h-6 bg-gray-200 rounded w-1/4 mx-auto mb-6"></div>
                    <div className="h-8 bg-gray-200 rounded w-3/4 mx-auto mb-8"></div>
                    <div className="bg-gray-100 p-6 rounded-lg">
                        <div className="flex items-center space-x-6">
                            <div className="w-48 h-48 bg-gray-200 rounded-lg"></div>
                            <div className="flex-1 space-y-4">
                                <div className="h-4 bg-gray-200 rounded w-1/4"></div>
                                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                                <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        );
    }

    return (
        <div className="w-full max-w-4xl mx-auto bg-white rounded-lg shadow-xl overflow-hidden">
            <div className="p-6">
                <h2 className="font-bold text-center mb-6 text-[#008ab8]">REAL STORIES</h2>
                <h3 className="text-3xl font-recoleta text-center mb-8">Users experience massive changes in less than a month</h3>
                <div className="relative">
                    <div className="flex items-center justify-between mb-4">
                        <button onClick={prevSlide} className="p-2 rounded-full bg-[#e6f3f7] text-[#008ab8] hover:bg-[#008ab8] hover:text-white transition-colors">
                            <ChevronLeft size={24} />
                        </button>
                        <button onClick={nextSlide} className="p-2 rounded-full bg-[#e6f3f7] text-[#008ab8] hover:bg-[#008ab8] hover:text-white transition-colors">
                            <ChevronRight size={24} />
                        </button>
                    </div>
                    <div className="bg-[#f0f9fc] p-6 rounded-lg">
                       
                        <div className="hidden sm:flex items-start gap-6">
                            <div className="flex-shrink-0">
                                <Image
                                    src={preloadedImages[currentIndex].src}
                                    alt={preloadedImages[currentIndex].alt}
                                    width={192}
                                    height={192}
                                    className="rounded-lg object-cover"
                                />
                            </div>
                            <div className="flex-grow">
                                <div className="flex mb-2">
                                    {[...Array(5)].map((_, i) => (
                                        <Star key={i} size={24} fill={i < feedbackData[currentIndex].rating ? "#008ab8" : "none"} color="#008ab8" />
                                    ))}
                                </div>
                                <h4 className="text-xl font-recoleta mb-2 text-[#008ab8]">{feedbackData[currentIndex].heading}</h4>
                                <p className="text-gray-700 mb-4">{feedbackData[currentIndex].description}</p>
                                <p className="text-[#008ab8] font-semibold">{feedbackData[currentIndex].name}</p>
                            </div>
                        </div>

                       
                        <div className="flex sm:hidden flex-col items-center">
                            <Image
                                src={preloadedImages[currentIndex].src}
                                alt={preloadedImages[currentIndex].alt}
                                width={192}
                                height={192}
                                className="rounded-lg object-cover mb-4"
                            />
                            <div className="text-center">
                                <div className="flex justify-center mb-2">
                                    {[...Array(5)].map((_, i) => (
                                        <Star key={i} size={20} fill={i < feedbackData[currentIndex].rating ? "#008ab8" : "none"} color="#008ab8" />
                                    ))}
                                </div>
                                <h4 className="text-xl font-recoleta mb-2 text-[#008ab8]">{feedbackData[currentIndex].heading}</h4>
                                <p className="text-gray-700 mb-4">{feedbackData[currentIndex].description}</p>
                                <p className="text-[#008ab8] font-semibold">{feedbackData[currentIndex].name}</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div className="flex justify-center mt-4">
                    {feedbackData.map((_, index) => (
                        <div
                            key={index}
                            className={`h-2 w-2 rounded-full mx-1 ${index === currentIndex ? 'bg-[#008ab8]' : 'bg-[#e6f3f7]'}`}
                        />
                    ))}
                </div>
            </div>
            <div className="bg-gray-100 p-4">
                <h3 className="font-semibold text-center mb-4">Psyllium Husk benefits covered in:</h3>
                <div className="flex justify-center items-center">
                    <Image src={featuredin} alt="Featured in" className="h-12 object-contain" />
                </div>
            </div>
        </div>
    );
};

const Summary4: React.FC = () => {
    return (
        <div className="bg-white rounded-lg shadow-xl p-4 sm:p-6 lg:p-8 w-full max-w-4xl z-10">
            <FeedbackCarousel />
        </div>
    );
};

export default Summary4;