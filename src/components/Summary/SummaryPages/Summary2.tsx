'use client'
import React from 'react';
import Image from 'next/image';

import { useSelector } from 'react-redux';
import metabolicage from '../../../../public/summary/gut.svg';
import age from '../../../../public/summary/age.svg';
import bmi from '../../../../public/summary/bmi.svg';
import height from '../../../../public/summary/height.svg';
import man from '../../../../public/summary/man.svg';
import weight from '../../../../public/summary/weight.svg';
import woman from '../../../../public/summary/woman.svg';

interface RootState {
    metrics: {
        age: string;
        heightFt: string;
        heightIn: string;
        heightCm: string;
        weight: string;
        desiredWeight: string;
        unit: 'imperial' | 'metric';
    };
    gender: {
        gender: string;
    };
}

const convertHeight = (heightFt: string, heightIn: string, heightCm: string, unit: 'imperial' | 'metric'): string => {
    if (unit === 'imperial') {
        return `${heightFt}'${heightIn}"`;
    } else {
        return `${heightCm} cm`;
    }
};

export default function Summary2() {
    const metrics = useSelector((state: RootState) => state.metrics);
    const { gender } = useSelector((state: RootState) => state.gender);

    const calculateBMI = (): number => {
        let heightInMeters: number;
        let weightInKg: number;

        if (metrics.unit === 'imperial') {
            const heightInInches = (parseInt(metrics.heightFt) * 12) + parseInt(metrics.heightIn);
            heightInMeters = heightInInches * 0.0254;
            weightInKg = parseFloat(metrics.weight) * 0.453592;
        } else {
            heightInMeters = parseInt(metrics.heightCm) / 100;
            weightInKg = parseFloat(metrics.weight);
        }

        return weightInKg / (heightInMeters * heightInMeters);
    };

    const calculateMetabolicAge = (): number => {
        const actualAge: number = parseInt(metrics.age);
        const bmi: number = calculateBMI();
        const metabolicAge: number = actualAge + (bmi - 22) * 2;
        return Math.max(18, Math.min(metabolicAge, 80));
    };

    const getMetabolicStatus = (metabolicAge: number, actualAge: number): string => {
        if (metabolicAge > actualAge) return "Your metabolism is slower than it should be";
        if (metabolicAge < actualAge) return "Your metabolism is faster than average";
        return "Your metabolism is normal for your age";
    };

    const getDisplayValues = (): { displayHeight: string; displayWeight: string } => {
        const displayHeight = convertHeight(
            metrics.heightFt,
            metrics.heightIn,
            metrics.heightCm,
            metrics.unit
        );

        const displayWeight = metrics.unit === 'imperial'
            ? `${metrics.weight} lbs`
            : `${metrics.weight} kg`;

        return { displayHeight, displayWeight };
    };

    const bmiValue: number = calculateBMI();
    const metabolicAgeValue: number = calculateMetabolicAge();
    const { displayHeight, displayWeight } = getDisplayValues();

    return (
        <div className="bg-white rounded-lg shadow-xl p-4 sm:p-6 lg:p-8 w-full max-w-4xl z-10">
            <div className="flex flex-col">
                <h1 className="font-recoleta text-3xl sm:text-4xl lg:text-5xl mb-4 text-center">
                    Your Personal Summary
                </h1>
                <p className="text-base sm:text-lg text-gray-600 mb-6 text-center">
                    Thanks for sharing! You&apos;re just a step away from reaching your desired goals.
                </p>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 lg:gap-6 mb-4 lg:mb-6">
                    <div className="bg-[#f0f9f6] p-4 lg:p-6 rounded-lg">
                        <div className="flex items-center justify-center mb-2 lg:mb-4">
                            <Image src={bmi} alt="BMI" width={32} height={32} className="mr-2" />
                            <span className="text-gray-600 text-lg lg:text-xl">Your BMI</span>
                        </div>
                        <p className="font-recoleta text-3xl lg:text-5xl text-center mb-2 lg:mb-4">
                            {bmiValue.toFixed(2)}
                        </p>
                        <div className="mt-2 bg-gray-200 h-2 lg:h-3 rounded-full">
                            <div
                                className="bg-green-500 h-2 lg:h-3 rounded-full"
                                style={{
                                    width: `${Math.max(0, Math.min(100, ((bmiValue - 18.5) / (30 - 18.5)) * 100))}%`,
                                    backgroundColor: bmiValue < 18.5 || bmiValue > 25 ? '#FFA500' : '#4CAF50'
                                }}
                            ></div>
                        </div>
                        <div className="flex justify-between text-xs lg:text-sm mt-1">
                            <span>18.5</span>
                            <span>25</span>
                            <span>30</span>
                        </div>
                    </div>
                    <div className="bg-[#f0f9f6] p-4 lg:p-6 rounded-lg">
                        <div className="flex items-center justify-center mb-2 lg:mb-4">
                            <Image src={metabolicage} alt="Metabolic Age" width={32} height={32} className="mr-2" />
                            <span className="text-gray-600 text-lg lg:text-xl">Metabolic age</span>
                        </div>
                        <p className="font-recoleta text-3xl lg:text-5xl text-center mb-2 lg:mb-4">
                            {metabolicAgeValue.toFixed(0)} years
                        </p>
                        <p className="text-sm lg:text-base text-gray-600 mt-2 text-center">
                            {getMetabolicStatus(metabolicAgeValue, parseInt(metrics.age))}
                        </p>
                    </div>
                </div>

                <div className="grid grid-cols-2 sm:grid-cols-4 gap-4 lg:gap-6">
                    <div className="bg-[#f0f9f6] p-4 lg:p-6 rounded-lg text-center">
                        <Image
                            src={gender === 'male' ? man : woman}
                            alt="Gender"
                            width={32}
                            height={32}
                            className="mx-auto mb-2 lg:mb-4"
                        />
                        <p className="font-recoleta text-lg lg:text-xl">
                            {gender === 'male' ? 'Male' : 'Female'}
                        </p>
                        <p className="text-xs lg:text-sm text-gray-600">Gender</p>
                    </div>
                    <div className="bg-[#f0f9f6] p-4 lg:p-6 rounded-lg text-center">
                        <Image
                            src={age}
                            alt="Age"
                            width={32}
                            height={32}
                            className="mx-auto mb-2 lg:mb-4"
                        />
                        <p className="font-recoleta text-lg lg:text-xl">{metrics.age}</p>
                        <p className="text-xs lg:text-sm text-gray-600">Age</p>
                    </div>
                    <div className="bg-[#f0f9f6] p-4 lg:p-6 rounded-lg text-center">
                        <Image
                            src={height}
                            alt="Height"
                            width={32}
                            height={32}
                            className="mx-auto mb-2 lg:mb-4"
                        />
                        <p className="font-recoleta text-lg lg:text-xl">{displayHeight}</p>
                        <p className="text-xs lg:text-sm text-gray-600">Height</p>
                    </div>
                    <div className="bg-[#f0f9f6] p-4 lg:p-6 rounded-lg text-center">
                        <Image
                            src={weight}
                            alt="Weight"
                            width={32}
                            height={32}
                            className="mx-auto mb-2 lg:mb-4"
                        />
                        <p className="font-recoleta text-lg lg:text-xl">{displayWeight}</p>
                        <p className="text-xs lg:text-sm text-gray-600">Weight</p>
                    </div>
                </div>
            </div>
        </div>
    );
}