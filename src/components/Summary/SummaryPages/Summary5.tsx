'use client'
import React, { useState } from 'react';
import Image from 'next/image';
import graph from '../../../../public/cf-graph.png';
import featuredin from '../../../../public/featured_plans.png';
import { useAppSelector } from '@/app/redux/hooks';
import { useRouter } from 'next/navigation';

export default function SummaryPage5() {
    const customerId = useAppSelector((state) => state.customer.id);
    const metrics = useAppSelector(state => state.metrics);
    const gender = useAppSelector(state => state.gender.gender);
    const [isLoading, setIsLoading] = useState(false);
    const router = useRouter()

    const handlePurchase = async () => {
        setIsLoading(true);
        try {
            const updateResponse = await fetch('/api/update-customer-metrics', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    customerId,
                    metrics,
                    gender
                }),
            });

            if (!updateResponse.ok) {
                throw new Error('Failed to update customer with metrics details');
            }

            router.push('/offer');
        } catch (error) {
            console.error('Error updating customer:', error);
        } finally {
            setIsLoading(false);
        }
    };

    return (
        <div className="bg-white rounded-lg shadow-xl p-4 sm:p-6 lg:p-8 w-full max-w-4xl z-10">
            <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
                <div className="mb-4 md:mb-0 md:mr-6 md:w-1/2">
                    <h2 className="text-4xl font-recoleta mb-4">Feeling All Clogged Up?</h2>
                    <p className="text-gray-600 mb-6">
                        Severe constipation can be a pretty vicious cycle. All the waste gets clogged in the colon, backs it up, and makes regular BMs a faraway dream... Not to mention what it does to your weight...
                    </p>
                    <button
                        onClick={handlePurchase}
                        className={`bg-[#008ab8] text-white font-bold py-3 px-6 rounded-full 
                        ${isLoading ? 'opacity-75 cursor-not-allowed' : 'hover:bg-[#006d8c]'} 
                        transition-colors duration-200`}
                        disabled={isLoading}
                    >
                        {isLoading ? (
                            <div className="flex items-center justify-center">
                                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                                Processing...
                            </div>
                        ) : (
                            'SEE THE PRODUCT'
                        )}
                    </button>
                </div>
                <div className="w-full md:w-1/2">
                    <h3 className="text-lg font-recoleta mb-2 text-wrap text-center">You could have up to: 2 kg of backed up waste</h3>

                    <div className="relative w-full h-64">
                        <Image
                            src={graph}
                            alt="Waste comparison graph"
                            layout="fill"
                            objectFit="contain"
                        />
                    </div>
                    <p className="text-sm text-gray-500 mt-2">*Based on scientific research about psyllium-husk</p>
                </div>
            </div>
            <div className="bg-gray-100 p-4 rounded-lg mt-6">
                <h3 className="font-semibold text-center mb-4 text-sm">Psyllium Husk benefits covered in:</h3>
                <div className="flex justify-center items-center">
                    <Image src={featuredin} alt="Featured in" className="h-12 object-contain" />
                </div>
            </div>
        </div>
    );
}