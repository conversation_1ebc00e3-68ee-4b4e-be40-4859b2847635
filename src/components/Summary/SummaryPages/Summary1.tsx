'use client'
import React, { useEffect, useState } from 'react';
import Image from 'next/image';

import { useSelector } from 'react-redux';

import wtchartinc from '../../../../public/weight_gain_chart.svg';
import wtchartdesc from '../../../../public/chart.svg';


interface RootState {
    metrics: {
        weight: string;
        desiredWeight: string;
        unit: 'imperial' | 'metric';
    };
}

export default function Summary1() {
    const [currentDate, setCurrentDate] = useState('');
    const [targetDate, setTargetDate] = useState('');
    const [isWeightLoss, setIsWeightLoss] = useState(false);

    const { weight, desiredWeight, unit } = useSelector((state: RootState) => state.metrics);

    const formatWeight = (weight: string, unit: 'imperial' | 'metric'): string => {
        return unit === 'imperial' ? `${weight} lbs` : `${weight} kg`;
    };

    useEffect(() => {
        if (weight && desiredWeight) {
            const weightDiff = Math.abs(parseFloat(desiredWeight) - parseFloat(weight));
            const weeksToTarget = Math.ceil(weightDiff / (unit === 'imperial' ? 1.76 : 0.8)); // 1.76 lbs = 0.8 kg

            const now = new Date();
            setCurrentDate(now.toLocaleString('default', { month: 'short', year: 'numeric' }));

            const targetDate = new Date(now.setDate(now.getDate() + weeksToTarget * 7));
            setTargetDate(targetDate.toLocaleString('default', { month: 'short', year: 'numeric' }));

            setIsWeightLoss(parseFloat(desiredWeight) < parseFloat(weight));
        }
    }, [weight, desiredWeight, unit]);

   

    return (
        
                <div className="bg-white rounded-lg shadow-xl p-4 sm:p-6 lg:p-8 w-full max-w-4xl z-10">
                    <div className="flex flex-col lg:flex-row">
                        <div className="w-full lg:w-1/2 lg:pr-4 mb-6 lg:mb-0">
                            <h1 className="font-recoleta text-3xl sm:text-4xl lg:text-5xl mb-4">
                                Why wait?<br />
                                Improve your<br />
                                life faster with<br />
                                ColonFit
                            </h1>
                            <p className="text-base sm:text-lg text-gray-600 mb-6">
                                Everyone does it, sometimes several times a day. It doesn&apos;t matter if you are young or old, rich or poor, a queen or the president.
                            </p>
                        </div>
                        <div className="w-full lg:w-1/2 lg:pl-4">
                            <h2 className="font-recoleta text-xl mb-4">Weight estimate*</h2>
                            <div className="flex justify-between items-center mb-4">
                                <div>
                                    <p className="font-recoleta text-sm">{currentDate}</p>
                                    <p className="font-recoleta text-xl sm:text-2xl">{formatWeight(weight, unit)}</p>
                                </div>
                                <div className="text-2xl sm:text-3xl text-green-500">&rarr;</div>
                                <div>
                                    <p className="font-recoleta text-sm">{targetDate}</p>
                                    <p className="font-recoleta text-xl sm:text-2xl">{formatWeight(desiredWeight, unit)}</p>
                                </div>
                            </div>
                            <div className="relative h-32 sm:h-40">
                                <Image
                                    src={isWeightLoss ? wtchartdesc : wtchartinc}
                                    alt="Weight Chart"
                                    layout="fill"
                                    objectFit="contain"
                                />
                            </div>
                        </div>
                    </div>
                </div>
            
    );
}