'use client'

import React, { memo } from 'react';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import Navbar from '@/components/Navbar/Navbar';
import recomBg from '../../../public/cfbg.jpg';

interface SummaryLayoutProps {
    children: React.ReactNode;
    currentQuestion: number;
    onContinue?: () => void;
}

const SummaryLayout: React.FC<SummaryLayoutProps> = memo(({
    children,
    currentQuestion,
    onContinue
}) => {
    const router = useRouter();

    const handleContinue = () => {
        if (onContinue) {
            onContinue();
        } else {
            router.push(`/summary/${currentQuestion + 1}`);
        }
    };

    return (
        <div className="min-h-screen flex flex-col relative">
            <div className="fixed top-0 left-0 right-0 z-50">
                <Navbar currentQuestion={currentQuestion} totalQuestions={5} />
            </div>
            <Image
                src={recomBg}
                alt="Background"
                layout="fill"
                objectFit="cover"
                quality={100}
                className="z-0"
            />
            <div className="flex-grow flex items-center justify-center px-4 sm:px-6 lg:px-8 pt-32 sm:pt-28 pb-24">
                {children}
            </div>
            <footer className="sticky bottom-0 bg-white w-full py-4 z-20">
                <div className="flex justify-center px-4">
                    <button
                        onClick={handleContinue}
                        className="w-full max-w-xs bg-[#008ab8] text-white font-bold py-3 px-4 rounded-lg hover:bg-[#006d8c] transition-colors duration-200 ease-in-out"
                    >
                        Continue
                    </button>
                </div>
            </footer>
        </div>
    );
});

SummaryLayout.displayName = 'SummaryLayout';

export default SummaryLayout;