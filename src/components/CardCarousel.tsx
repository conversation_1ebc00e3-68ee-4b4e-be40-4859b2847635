import React, { useState } from "react";
import Image from "next/image";

interface Card {
  title: string;
  description: string;
  imageUrl: string;
  backgroundColor?: string;
  textColor?: string;
  subtitle?: string;
  bulletPoints?: string[];
  footer?: string;
  iconUrl?: string;
  [key: string]: any;
}

interface CardCarouselProps {
  cards: Card[];
}

const CardCarousel: React.FC<CardCarouselProps> = ({ cards }) => {
  const [current, setCurrent] = useState(0);
  const total = cards.length;

  const goTo = (idx: number) => {
    setCurrent((idx + total) % total);
  };

  const card = cards[current];

  return (
    <div className="relative w-full flex flex-col items-center">
      {/* Card with relative positioning for arrows */}
      <div className="relative w-full max-w-md mx-auto rounded-3xl flex flex-col items-center justify-center p-8 shadow transition-all duration-500" style={{ backgroundColor: card.backgroundColor || "#FFF5E5", color: card.textColor || "#184C43" }}>
        {/* Left Arrow */}
        <button
          className="absolute left-2 top-1/2 -translate-y-1/2 bg-white/80 rounded-full p-2 shadow z-10"
          style={{ transform: "translateY(-50%)" }}
          onClick={() => goTo(current - 1)}
          aria-label="Previous Card"
        >
          &#8592;
        </button>
        {/* Right Arrow */}
        <button
          className="absolute right-2 top-1/2 -translate-y-1/2 bg-white/80 rounded-full p-2 shadow z-10"
          style={{ transform: "translateY(-50%)" }}
          onClick={() => goTo(current + 1)}
          aria-label="Next Card"
        >
          &#8594;
        </button>
        {/* Card Content */}
        {card.subtitle && (
          <div className="text-center text-base font-semibold mb-2 opacity-80">
            {card.subtitle}
          </div>
        )}
        <div className="text-center font-bold text-xl mb-2 flex items-center justify-center" style={{ color: card.textColor || undefined }}>
          {card.iconUrl && (
            <Image
              src={card.iconUrl}
              alt="icon"
              width={28}
              height={28}
              className="inline-block align-middle mr-2"
              priority
            />
          )}
          {card.title}
        </div>
        <div className="text-justify text-base mb-6 opacity-80" style={{ color: card.textColor || undefined }}>
          {card.description}
        </div>
        {card.imageUrl && (
          <div className="flex items-center justify-center mb-4 w-full">
            <div className="w-full max-w-xs mx-auto">
              <Image
                src={card.imageUrl}
                alt={card.title}
                width={400}
                height={400}
                className="object-contain w-full h-auto"
                priority
              />
            </div>
          </div>
        )}
        {card.bulletPoints && card.bulletPoints.length > 0 && (
          <ul className="text-base space-y-2 w-full mb-2">
            {card.bulletPoints.map((point: string, idx: number) => (
              <li key={idx} className="flex items-start">
                <span className="mr-2 mt-1">✱</span> {point}
              </li>
            ))}
          </ul>
        )}
        {card.footer && (
          <div className="text-center text-xs mt-2 opacity-70">{card.footer}</div>
        )}
      </div>
      {/* Dots below the card */}
      <div className="flex justify-center mt-4 gap-2 items-center">
        {cards.map((_, idx) => (
          <button
            key={idx}
            className={`w-2 h-2 rounded-full ${idx === current ? "bg-green-600" : "bg-gray-300"}`}
            onClick={() => goTo(idx)}
            aria-label={`Go to card ${idx + 1}`}
          />
        ))}
      </div>
    </div>
  );
};

export default CardCarousel; 