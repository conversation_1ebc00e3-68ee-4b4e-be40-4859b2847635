import React from 'react';
import Image from 'next/image';
import { useAppSelector } from '@/app/redux/hooks';
import { RootState } from '@/app/redux/store';
const NowToGoal = () => {
    const gender = useAppSelector((state: RootState) => state.gender.gender);

    const imagePath = gender === 'female'
        ? '/offerpage/beforeafterfemale.jpg'
        : '/offerpage/before-after.jpg';

    return (
        <div className="w-full max-w-xl mx-auto bg-white rounded-3xl shadow-lg p-5">
            <div className="flex items-center justify-center gap-4 mb-2">
                <div className="flex-1 text-center">
                    <span className="text-xl font-semibold text-gray-800">Now</span>
                </div>
                <span className="text-blue-500 text-2xl font-bold flex-shrink-0">&raquo;</span>
                <div className="flex-1 text-center">
                    <span className="text-xl font-semibold text-gray-800">Goal</span>
                </div>
            </div>
            <div className="relative w-full aspect-[4/3] max-w-lg mx-auto">
                <div className="absolute inset-0 rounded-3xl overflow-hidden">
                    <Image
                        src={imagePath}
                        alt="Now to Goal Comparison"
                        fill
                        className="object-cover"
                    />
                </div>
            </div>
        </div>
    );
};

export default NowToGoal;