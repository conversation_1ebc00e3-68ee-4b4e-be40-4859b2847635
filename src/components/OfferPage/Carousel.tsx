'use client';

import { useState } from 'react';
import Image from 'next/image';
import carousel1 from '@/../public/offerpage/homescreen.png';
import carousel2 from '@/../public/offerpage/profile.png';
import carousel3 from '@/../public/offerpage/Recipes.png';

const Carousel = () => {
    const [currentSlide, setCurrentSlide] = useState(0);

    const slides = [
        { image: carousel1, alt: "Calorie Tracker App Screen 1" },
        { image: carousel2, alt: "Calorie Tracker App Screen 2" },
        { image: carousel3, alt: "Calorie Tracker App Screen 3" },
    ];

    const nextSlide = () => {
        setCurrentSlide((prev) => (prev === slides.length - 1 ? 0 : prev + 1));
    };

    const prevSlide = () => {
        setCurrentSlide((prev) => (prev === 0 ? slides.length - 1 : prev - 1));
    };

    const goToSlide = (slideIndex: number) => {
        setCurrentSlide(slideIndex);
    };

    return (
        <div className="max-w-xl mx-auto px-4 py-8">
           
            <h2 className="text-2xl md:text-3xl font-bold text-gray-900 text-center mb-8">
                Maximize Your Results with our
                Calorie Tracker App
            </h2>
            <div className="relative w-full max-w-md mx-auto">
                <div className="overflow-hidden rounded-3xl flex items-center justify-center">
                    <div
                        className="flex transition-transform duration-500 ease-out w-full"
                        style={{ transform: `translateX(-${currentSlide * 100}%)` }}
                    >
                        {slides.map((slide, index) => (
                            <div key={index} className="w-full flex-shrink-0 flex items-center justify-center">
                                <Image
                                    src={slide.image}
                                    alt={slide.alt}
                                    className="w-auto h-auto object-contain"
                                    priority={index === 0}
                                />
                            </div>
                        ))}
                    </div>
                </div>

                <button
                    onClick={prevSlide}
                    className="absolute left-0 top-1/2 -translate-y-1/2 -translate-x-4 w-8 h-8 bg-white rounded-full shadow-md flex items-center justify-center hover:bg-gray-50 transition-colors"
                    aria-label="Previous slide"
                >
                    <svg className="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                    </svg>
                </button>
                <button
                    onClick={nextSlide}
                    className="absolute right-0 top-1/2 -translate-y-1/2 translate-x-4 w-8 h-8 bg-white rounded-full shadow-md flex items-center justify-center hover:bg-gray-50 transition-colors"
                    aria-label="Next slide"
                >
                    <svg className="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                </button>

                <div className="absolute -bottom-6 left-1/2 -translate-x-1/2 flex gap-2">
                    {slides.map((_, index) => (
                        <button
                            key={index}
                            onClick={() => goToSlide(index)}
                            className={`w-2 h-2 rounded-full transition-colors ${currentSlide === index ? 'bg-blue-500' : 'bg-gray-300'}`}
                            aria-label={`Go to slide ${index + 1}`}
                        />
                    ))}
                </div>
            </div>
        </div>
    );
};

export default Carousel;