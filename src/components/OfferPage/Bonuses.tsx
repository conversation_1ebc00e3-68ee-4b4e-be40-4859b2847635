import React from 'react';
import Image, { StaticImageData } from 'next/image';
import img1 from '@/../public/offerpage/personalisedGutHealth.png';
import img2 from '@/../public/offerpage/longTermGutHealth.png';
import img3 from '@/../public/offerpage/aiCalorieTracker.png';
import img4 from '@/../public/offerpage/bonusGift.png';
import gift from '@/../public/offerpage/gift.png';

interface BonusCard {
    image: StaticImageData;
    title: string;
    originalPrice: number;
    description: string;
}

interface BonusesProps {
    cards: BonusCard[];
}

export const bonusCardsData = [
    {
        image: img1,
        title: "Personalized Gut Health Meal Plan",
        originalPrice: 29.99,
        description: "Customized meals to enhance digestion and nutrient absorption."
    },
    {
        image: img2,
        title: "Long-Term Gut Health Guide",
        originalPrice: 9.10,
        description: "A comprehensive resource to help you maintain optimal gut health for years to come."
    },
    {
        image: img3,
        title: "AI Calorie Tracker (1-Month Access)",
        originalPrice: 30,
        description: "Smart tracking app to monitor your diet and stay on track."
    },
    {
        image: img4,
        title: "BONUS gift with your order",
        originalPrice: 29.99,
        description: "A special surprise just for you! An exclusive reward to make your journey even better!"
    }
];

const Bonuses: React.FC<BonusesProps> = ({ cards }) => {
    return (
        <div className="w-full max-w-3xl mx-auto px-4 sm:px-6 py-6 sm:py-8">
            <div className="flex items-center gap-2 mb-4 sm:mb-8">
                <div className="w-6 h-6 sm:w-8 sm:h-8 relative">
                    <Image
                        src={gift}
                        alt="Bonus gift"
                        className="object-contain"
                        fill
                    />
                </div>
                <h2 className="text-xl sm:text-2xl font-recoleta">
                    Get These Amazing Bonuses Absolutely Free!
                </h2>
            </div>

            <div className="space-y-3 sm:space-y-4">
                {cards.map((card, index) => (
                    <div
                        key={index}
                        className="bg-white rounded-lg p-3 sm:p-6 shadow-sm border border-gray-100"
                    >
                        <div className="flex items-start gap-3 sm:gap-4">
                            <div className="w-12 h-12 sm:w-16 sm:h-16 relative flex-shrink-0">
                                <Image
                                    src={card.image}
                                    alt={card.title}
                                    className="object-contain"
                                    fill
                                />
                            </div>

                            <div className="flex-1 min-w-0">
                                <div className="flex flex-row items-start justify-between gap-2 mb-1 sm:mb-2">
                                    <h3 className="text-base sm:text-xl font-semibold leading-tight">
                                        {card.title}
                                    </h3>
                                    <div className="flex items-center gap-2 flex-shrink-0">
                                        <span className="text-sm sm:text-lg line-through text-gray-400">
                                            ${card.originalPrice.toFixed(2)}
                                        </span>
                                        <span className="px-2 py-0.5 sm:px-3 sm:py-1 bg-blue-50 text-blue-600 rounded-md font-medium text-sm">
                                            Free
                                        </span>
                                    </div>
                                </div>
                                <p className="text-gray-600 text-sm sm:text-base leading-tight sm:leading-normal">
                                    {card.description}
                                </p>
                            </div>
                        </div>
                    </div>
                ))}
            </div>
        </div>
    );
};

export default Bonuses;