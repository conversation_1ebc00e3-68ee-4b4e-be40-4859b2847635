import { FC } from 'react';
import useOfferTimer from '@/hooks/useOfferTimer';

interface NavbarProps {
    onScrollToOffers: () => void;
}

const Navbar: FC<NavbarProps> = ({ onScrollToOffers }) => {
    const { timeLeft, formatTime } = useOfferTimer();

    return (
        <div className="bg-white shadow-md fixed top-0 left-0 right-0 z-50">
            <div className="max-w-7xl mx-auto px-4 py-3">
                <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-1">
                        <span className="text-gray-600 text-sm">
                            65% Offer reserved for
                        </span>
                        <span className="font-bold text-black">
                            {formatTime(timeLeft)}
                        </span>
                    </div>
                    <button
                        onClick={onScrollToOffers}
                        className="bg-[#008ab8] hover:bg-[#006a8f] text-white px-4 py-2 rounded-full text-sm font-medium"
                    >
                        Take this offer
                    </button>
                </div>
            </div>
        </div>
    );
};

export default Navbar;