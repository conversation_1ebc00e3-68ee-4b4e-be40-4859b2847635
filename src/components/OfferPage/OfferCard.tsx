import React from 'react';
import Image from 'next/image';

interface OfferCardProps {
    image: string;
    bannerText: string;
    bannerColor: string;
    title: string;
    price: {
        amount: string;
        period: string;
    };
    bulletPoints: string[];
    buttonText: string;
    onButtonClick?: () => void;
    saleText?: string;
}

const OfferCard = ({
    image,
    bannerText,
    bannerColor,
    title,
    price,
    bulletPoints,
    buttonText,
    onButtonClick,
    saleText
}: OfferCardProps) => {
    return (
        <div className="bg-white rounded-2xl shadow-lg overflow-hidden h-[700px] flex flex-col">
            
            <div
                className="text-white text-center py-2 text-sm font-medium w-full h-[40px]"
                style={{ backgroundColor: bannerColor }}
            >
                {bannerText}
            </div>
            <div className="flex flex-col h-[660px] p-6">
                <div className="h-[260px]">
                    <div className="h-[160px] mb-6 flex items-center justify-center">
                        <div className="relative w-[140px] h-[140px]">
                            <Image
                                src={image}
                                alt={title}
                                layout="fill"
                                objectFit="contain"
                                priority
                            />
                        </div>
                    </div>

                    <h3 className="text-xl font-bold mb-2 h-[28px] overflow-hidden ">{title}</h3>
                    <div className="flex items-baseline h-[32px]">
                        <span className="text-2xl font-semibold font-recoleta">{price.amount}</span>
                        <span className="text-sm text-gray-600 ml-1">{price.period}</span>
                    </div>
                </div>

                <div className="flex-1 overflow-y-auto">
                    <ul className="space-y-3">
                        {bulletPoints.map((point, index) => (
                            <li key={index} className="flex items-start gap-2">
                                <svg
                                    className="w-5 h-5 text-green-500 mt-0.5 flex-shrink-0"
                                    fill="none"
                                    stroke="currentColor"
                                    viewBox="0 0 24 24"
                                >
                                    <path
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                        strokeWidth="2"
                                        d="M5 13l4 4L19 7"
                                    />
                                </svg>
                                <span className="text-gray-700 text-sm">{point}</span>
                            </li>
                        ))}
                    </ul>
                </div>
                <div className="h-[100px] pt-6">
                    <button
                        onClick={onButtonClick}
                        className="w-full bg-[#008CB0] hover:bg-[#007BA8] text-white font-medium py-3 px-4 rounded-lg transition-colors"
                    >
                        {buttonText}
                    </button>

                    {saleText && (
                        <div className="text-center mt-4 text-sm font-medium text-gray-600">
                            {saleText}
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
};

export default OfferCard;