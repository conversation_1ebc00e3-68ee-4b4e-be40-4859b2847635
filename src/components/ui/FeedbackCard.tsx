import React from 'react';
import { Star, CheckCircle } from 'lucide-react';

interface FeedbackCardProps {
    rating: number;
    heading: string;
    description: string;
    name: string;
}

const FeedbackCard: React.FC<FeedbackCardProps> = ({ rating, heading, description, name }) => {
    return (
        <div className="bg-white rounded-lg shadow-md p-6 max-w-sm mx-auto text-center">
            <div className="flex justify-center mb-2">
                {[...Array(5)].map((_, index) => (
                    <Star
                        key={index}
                        size={24}
                        className={index < rating ? 'text-yellow-400 fill-current' : 'text-gray-300'}
                    />
                ))}
            </div>
            <h2 className="font-recoleta text-xl font-bold mb-2">{heading}</h2>
            <p className="text-gray-600 mb-4">{description}</p>
            <div className="flex items-center justify-center">
                <span className="font-semibold mr-2">{name}</span>
                <CheckCircle size={16} className="text-blue-500" />
                <span className="text-sm text-blue-500 ml-1">Verified customer</span>
            </div>
        </div>
    )
}

export default FeedbackCard;