import Image from 'next/image';
import { <PERSON><PERSON>utt<PERSON> } from './HeroButton';

export default function HeroComponent() {
  return (
    <div className="min-h-screen w-full flex flex-col items-center justify-center bg-gradient-to-br from-[#0F403D] via-[#0E6661] to-[#0F403D] relative overflow-hidden">
      {/* Background pattern overlay (optional, for Figma look) */}
      <div className="absolute inset-0 pointer-events-none z-0">
        {/* You can add a subtle SVG or pattern here if you want to match the Figma background more closely */}
      </div>
      <div className="relative z-10 flex flex-col items-center justify-center w-full max-w-xl px-4 py-12">
        <h1 className="text-white text-center font-serif font-bold text-2xl sm:text-3xl md:text-4xl lg:text-4xl mb-2 leading-tight">
          Discover Your Personal<br />
          Lymphatic-Drainage Plan
        </h1>
        <p className="text-white/70 text-base md:text-lg mb-2">
          Let&apos;s begin your journey to better skin.
        </p>
        <HeroButton />
        <div className="w-full flex justify-center mt-2">
          <Image
            src="/images/hero.svg"
            alt="Lymphatic Drainage Illustration"
            width={700}
            height={400}
            className="w-auto h-auto"
            priority
          />
        </div>
      </div>
    </div>
  );
}
