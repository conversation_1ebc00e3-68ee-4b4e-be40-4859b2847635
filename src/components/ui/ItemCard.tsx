import React from 'react';
import { Check } from 'lucide-react';

interface ItemCardProps {
    popularTag?: string;
    title: string;
    bannerColor: string;
    supply: string;
    price: string;
    bulletPoints: string[];
    isSelected: boolean;
}

interface CircularIconProps {
    isSelected: boolean;
}

const CircularIcon: React.FC<CircularIconProps> = ({ isSelected }) => (
    <div className="absolute left-0 top-1">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="flex-shrink-0">
            <circle cx="12" cy="12" r="9.5" fill={isSelected ? "#008ab1" : "white"} stroke="#008ab1" strokeWidth={isSelected ? "0" : "1"} />
            {isSelected && (
                <path d="M9 12L11 14L15 10" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
            )}
        </svg>
    </div>
);

const getTextColor = (backgroundColor: string): string => {
    return backgroundColor === 'bg-[#00b7ee]' ? 'text-white' : 'text-black';
};

const getHeadingColor = (headingColor: string): string => {
    return headingColor === 'bg-[#00b7ee]' ? 'bg-[#008ab1]' : 'bg-[#00b7ee]';
};

const ItemCard: React.FC<ItemCardProps> = ({
    popularTag,
    title,
    bannerColor,
    supply,
    price,
    bulletPoints,
    isSelected
}) => {
    const textColor = getTextColor(bannerColor);
    const headingColor = getHeadingColor(bannerColor);

    return (
        <div className={`bg-white rounded-lg  overflow-hidden relative ${isSelected ? 'border-2 border-[#008ab1] shadow-2xl' : ''}`}>
            <div className="relative">
                {popularTag && (
                    <div className={`${bannerColor} ${textColor} text-xs font-bold px-2 py-1 text-center`}>
                        {popularTag}
                    </div>
                )}
                <div className={`${headingColor} ${textColor} text-sm p-2 text-center flex items-center justify-center`}>
                    {title}
                </div>
            </div>
            <div className="p-3 flex flex-col h-full">
                <div className="relative pl-7 mb-2">
                    <CircularIcon isSelected={isSelected} />
                    <div className="text-center">
                        <span className="text-3xl font-recoleta text-black">{price}</span>
                        <span className="text-sm font-semibold text-gray-600 ml-1">/ bottle</span>
                    </div>
                </div>
                <div className="text-center mb-2">
                    <span className="text-xs text-gray-600">{supply}</span>
                </div>
                <div className="flex-grow flex items-center justify-center">
                    <ul className="space-y-2">
                        {bulletPoints.map((point, index) => (
                            <li key={index} className="flex items-center">
                                <Check className="w-4 h-4 mr-2 text-[#008ab1] flex-shrink-0" />
                                <span className="text-xs text-gray-700">{point}</span>
                            </li>
                        ))}
                    </ul>
                </div>
            </div>
        </div>
    );
};

export default ItemCard;