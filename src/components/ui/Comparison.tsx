"use client";
import React from "react";
import Image from "next/image";

interface ComparisonProps {
  beforeImage: string;
  afterImage: string;
  beforeLabel?: string;
  afterLabel?: string;
  className?: string;
}

export function Comparison({
  beforeImage,
  afterImage,
  beforeLabel = "Before",
  afterLabel = "After",
  className = "",
}: ComparisonProps) {
  return (
    <div className={`relative w-full aspect-[4/3] ${className}`}>
      <div className="absolute inset-0 flex">
        {/* Before Image */}
        <div className="relative w-1/2 h-full">
          <Image
            src={beforeImage}
            alt="Before"
            fill
            className="object-cover"
          />
          <div className="absolute bottom-0 left-0 right-0 bg-black/50 text-white text-xs py-1 px-2 text-center">
            {beforeLabel}
          </div>
        </div>
        {/* After Image */}
        <div className="relative w-1/2 h-full">
          <Image
            src={afterImage}
            alt="After"
            fill
            className="object-cover"
          />
          <div className="absolute bottom-0 left-0 right-0 bg-black/50 text-white text-xs py-1 px-2 text-center">
            {afterLabel}
          </div>
        </div>
      </div>
    </div>
  );
}

export default Comparison; 