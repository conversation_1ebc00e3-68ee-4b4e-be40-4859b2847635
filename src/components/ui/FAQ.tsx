import React, { useState } from "react";
import { ChevronDown, ChevronUp } from "lucide-react";

type FAQItem = {
  question: string;
  answer: React.ReactNode;
};

const FAQItem: React.FC<FAQItem & { isOpen: boolean; toggle: () => void }> = ({
  question,
  answer,
  isOpen,
  toggle,
}) => (
  <div className="border-b border-gray-200">
    <button
      className="flex justify-between items-center w-full py-5 text-left"
      onClick={toggle}
    >
      <span className=" font-recoleta text-lg font-medium text-gray-900">
        {question}
      </span>
      {isOpen ? (
        <ChevronUp className="h-5 w-5 text-gray-500" />
      ) : (
        <ChevronDown className="h-5 w-5 text-gray-500" />
      )}
    </button>
    {isOpen && <div className="pb-5 pr-12">{answer}</div>}
  </div>
);

const FAQ: React.FC = () => {
  const [openIndex, setOpenIndex] = useState<number | null>(null);

  const faqItems: FAQItem[] = [
    {
      question: "What ingredients are used in ColonFit?",
      answer: (
        <>
          <p className="text-base text-gray-500 mb-4">
            This is the full ColonFit ingredients list: Psyllium Husk Powder,
            Natural Flavorings, Citric Acid, Crystallized Lemon (citric acid,
            lemon oil, lemon juice), Stevia Leaf Extract, Sea Salt, Fruit &amp;
            Vegetable Juice (color), Rice Hulls.
          </p>
          <ul className="space-y-4">
            <li>
              <h4 className="font-medium text-gray-900">Psyllium husk</h4>
              <p className="text-gray-500">
                Psyllium husk is a fiber that works as a gentle bulk-forming
                laxative. It attracts water and becomes a viscous compound that
                benefits constipation, diarrhea, blood sugar, cholesterol.
              </p>
            </li>
            <li>
              <h4 className="font-medium text-gray-900">Sodium</h4>
              <p className="text-gray-500">
                Sodium is a mineral found in salt and many other foods. Your
                body needs sodium for normal muscle and nerve functions. It also
                helps keep your body fluids in balance.
              </p>
            </li>
            <li>
              <h4 className="font-medium text-gray-900">Stevia leaf extract</h4>
              <p className="text-gray-500">
                Stevia is a natural sweetener, often used as a safer and
                healthier sugar substitute. It has several health benefits, such
                as reduced calorie intake, blood sugar levels, and risk of
                cavities.
              </p>
            </li>
            <li>
              <h4 className="font-medium text-gray-900">Citric acid</h4>
              <p className="text-gray-500">
                It&apos;s naturally present in citric fruits, such as lemons and
                limes. It works as a naturally occurring antioxidant, which may
                help speed up your metabolism, fight infections, and boost your
                energy.
              </p>
            </li>
            <li>
              <h4 className="font-medium text-gray-900">Natural flavoring</h4>
              <p className="text-gray-500">
                ColonFit only uses natural flavoring without any artificial
                additives or sweeteners. It creates a delicate yet sweet taste
                without any added sugars.
              </p>
            </li>
          </ul>
          <p className="text-base text-gray-500 mt-4">
            Want to learn more about our supplement facts? Find ColonFit&apos;s
            full ingredient list{" "}
            <a href="#" className="text-blue-500 hover:underline">
              here
            </a>
            .
          </p>
        </>
      ),
    },
    {
      question: "Can ColonFit cause bloating?",
      answer:
        "Dietary fiber and other ColonFit ingredients are generally well-tolerated and safe. If you're new to using ColonFit, you might feel a little bloated for the first few days of using it. That's completely normal, your body needs a little time to adjust to the increased fiber intake.",
    },
    {
      question: "Can I use ColonFit while on the Keto diet?",
      answer:
        "Yes, you can! Dietary fiber, unlike other macronutrients such as fats, proteins, or carbohydrates — which the body breaks down and absorbs — isn't digested by your body because it has no macronutrients.",
    },
    {
      question: "When I will feel the effects of ColonFit?",
      answer:
        "Feeling a change depends on the individual metabolic response and varies from person to person. On average, people feel a change in their bowel movements after 24-72 hours.",
    },
    {
      question: "Is ColonFit just a laxative?",
      answer:
        "No, ColonFit is not a laxative, it's a dietary fiber supplement.",
    },
    {
      question: "Can I pay for ColonFit upon delivery?",
      answer:
        "Unfortunately, payment upon delivery of your package is not possible. However, you can pay via Apple Pay/Google Pay, Debit/Credit Card and Klarna payment methods in our checkout page.",
    },
    {
      question: "What is the ColonFit Program?",
      answer:
        "The ColonFit program is a weight management program that helps you take better care of your body, improve your wellness, and manage weight. Together with the ColonFit fiber supplement, it can help you lose weight through easy-to-follow exercises and small but meaningful nutritional changes based on leading wellness practices. The program complements the beneficial qualities of Psyllium Husk, the main ingredient in ColonFit, which may help you feel more satiated, control food intake, and manage weight. Studies show that increasing your fiber intake and other dietary and physical activity interventions may promote weight loss. The program is perfect for making these changes easy and enjoyable!",
    },
  ];

  return (
    <div className="max-w-3xl mx-auto px-4 py-16 sm:px-6 sm:py-24 lg:px-8">
      <h2 className=" font-recoleta text-3xl  text-center text-gray-900 sm:text-4xl">
        Frequently asked questions by our customers
      </h2>
      <div className="mt-12">
        {faqItems.map((item, index) => (
          <FAQItem
            key={index}
            question={item.question}
            answer={item.answer}
            isOpen={index === openIndex}
            toggle={() => setOpenIndex(index === openIndex ? null : index)}
          />
        ))}
      </div>
    </div>
  );
};

export default FAQ;
