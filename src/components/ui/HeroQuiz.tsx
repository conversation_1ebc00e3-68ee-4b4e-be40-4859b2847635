'use client'
import React, { useTransition } from 'react';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import logo from '../../../public/logo/CF LOGO WHITE PNG.png';
import heropng from '../../../public/plans.png';
import feature1 from '../../../public/featuredin/1.webp'
import feature2 from '../../../public/featuredin/2.webp'
import feature3 from '../../../public/featuredin/3.webp'
import feature4 from '../../../public/featuredin/4.webp'
import feature5 from '../../../public/featuredin/5.webp'
import { useAppDispatch } from '@/app/redux/hooks';
import { setGender } from '@/app/redux/genderSlice';
import { useQuiz } from '@/app/quizContext';

const CircularCheckMark = () => (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <circle cx="12" cy="12" r="10" fill="#4CAF50" />
        <path d="M9 12L11 14L15 10" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
    </svg>
);

export default function HeroQuiz() {
    const router = useRouter();
    const [isPending, startTransition] = useTransition();
    const dispatch1 = useAppDispatch()
    const { dispatch } = useQuiz();
    
    const handleGenderSelect = (gender: string) => () => {
        startTransition(() => {
            dispatch1(setGender(gender))
            dispatch({ type: 'SET_GENDER', payload: gender });
            router.push(`/questions/${gender}/1`);
        });
    };

    return (
        <div className="relative w-full min-h-screen flex flex-col text-white overflow-hidden bg-gray-800">
            <nav className="absolute top-0 left-0 right-0 z-30 p-4 flex justify-center">
                <Image
                    src={logo}
                    alt="ColonFit Logo"
                    width={120}
                    height={40}
                    objectFit="contain"
                />
            </nav>

            <div className="absolute inset-0 z-0">
                <Image
                    src={heropng}
                    alt="Hero Background"
                    layout="fill"
                    objectFit="cover"
                    quality={100}
                />
            </div>

            <div className="absolute z-10 inset-0 bg-black opacity-50"></div>

            <div className="relative z-20 flex-grow flex flex-col items-center justify-center w-full max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-20 mt-16 sm:mt-0">
                <div className="text-center space-y-6">
                    <h2 className=" font-recoleta text-2xl sm:text-3xl md:text-4xl lg:text-5xl ">Take control of your weight with daily bloat relief</h2>
                    <ul className="text-sm sm:text-base md:text-lg space-y-2 inline-block text-left">
                        {[
                            "Lose weight",
                            "Reduce bloating",
                            "Relieve constipation",
                            "Increase your fiber intake"
                        ].map((item, index) => (
                            <li key={index} className="flex items-center">
                                <CircularCheckMark />
                                <span className="ml-2">{item}</span>
                            </li>
                        ))}
                    </ul>

                    <p className="font-recoleta text-lg sm:text-xl lg:text-2xl  mt-8">Select your gender</p>
                    <div className="flex flex-col items-center space-y-6 mt-4">
                        <div className="flex justify-center space-x-4">
                            <button className="bg-pink-500 text-white px-6 sm:px-8 py-2 sm:py-3 rounded-full text-base sm:text-lg font-semibold hover:opacity-90 transition-opacity w-28 sm:w-36" onClick={handleGenderSelect('female')}>
                                {isPending ? (
                                    <>
                                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-600 mr-2"></div>

                                    </>
                                ) : (
                                    'Female'
                                )}
                            </button>
                            <button className="bg-[#006699] text-white px-6 sm:px-8 py-2 sm:py-3 rounded-full text-base sm:text-lg font-semibold hover:opacity-90 transition-opacity w-28 sm:w-36" onClick={handleGenderSelect('male')}>
                                {isPending ? (
                                    <>
                                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-600 mr-2"></div>

                                    </>
                                ) : (
                                    'Male'
                                )}
                            </button>
                        </div>

                        <div className="relative bg-gray-900 text-white px-4 py-2 rounded-lg shadow-lg text-center w-full max-w-sm">
                            <div className="absolute -top-2 left-1/4 transform -translate-x-1/2 w-0 h-0 border-l-8 border-r-8 border-b-8 border-transparent border-b-gray-900"></div>
                            <div className="absolute -top-2 right-1/4 transform translate-x-1/2 w-0 h-0 border-l-8 border-r-8 border-b-8 border-transparent border-b-gray-900"></div>
                            <p className="font-bold text-xs sm:text-sm md:text-base">
                                BACK IN SHAPE SALE UP TO <span className="text-yellow-400">65% OFF!</span>
                            </p>
                        </div>
                    </div>

                    <div className="mt-16">
                        <p className="text-xs sm:text-sm font-bold uppercase mb-4">COLONFIT IS FEATURED IN:</p>
                        <div className="flex flex-wrap justify-center items-center gap-4">
                            {[feature1, feature2, feature3, feature4, feature5].map((feature, index) => (
                                <Image
                                    key={index}
                                    src={feature}
                                    alt={`Featured in ${index + 1}`}
                                    width={80}
                                    height={40}
                                    objectFit="contain"
                                />
                            ))}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
}