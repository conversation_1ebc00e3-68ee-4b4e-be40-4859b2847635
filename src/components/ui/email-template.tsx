import * as React from "react";

interface EmailTemplateProps {
    firstName: string;
    productName: string;
    shippingAddress: {
        streetAddress: string;
        apartment?: string;
        city: string;
        state: string;
        zipCode: string;
        country: string;
    };
    totalAmount: number;
}

export const EmailTemplate: React.FC<Readonly<EmailTemplateProps>> = ({
    firstName,
    productName,
    shippingAddress,
    totalAmount,
}) => {
    const getProductImage = (): string => {
        return "https://lymph.app/logo/lymph-logo.png"; // Update with real Lymph product image if available
    };

    return (
        <div style={{ fontFamily: 'Arial, sans-serif', maxWidth: '600px', margin: '0 auto', padding: '20px', backgroundColor: '#f8f8f8', borderRadius: '8px' }}>
            <div style={{ textAlign: 'center', marginBottom: '20px' }}>
                {/* eslint-disable-next-line @next/next/no-img-element */}
                <img src="https://lymph.app/logo/lymph-logo.png" alt="Lymph Logo" width="150" height="75" style={{ maxWidth: '100%', height: 'auto' }} />
            </div>
            <div style={{ backgroundColor: '#ffffff', padding: '20px', borderRadius: '8px', boxShadow: '0 2px 4px rgba(0,0,0,0.1)' }}>
                <h1 style={{ color: '#184C43', fontSize: '24px', marginBottom: '20px' }}>Thank you for your purchase, {firstName}!</h1>
                <p style={{ fontSize: '16px', lineHeight: '1.5' }}>We&apos;re excited to confirm your order for:</p>
                <h2 style={{ color: '#333333', fontSize: '20px', marginBottom: '10px' }}>{productName}</h2>
                <div style={{ textAlign: 'center', margin: '20px 0' }}>
                    {/* eslint-disable-next-line @next/next/no-img-element */}
                    <img src={getProductImage()} alt={productName} width="200" height="200" style={{ maxWidth: '100%', height: 'auto' }} />
                </div>
                <p style={{ fontSize: '18px', fontWeight: 'bold', marginBottom: '20px' }}>Total Amount: ${totalAmount.toFixed(2)}</p>
                <h3 style={{ color: '#184C43', fontSize: '18px', marginBottom: '10px' }}>Shipping Address:</h3>
                <p style={{ fontSize: '16px', lineHeight: '1.5' }}>
                    {shippingAddress.streetAddress}
                    {shippingAddress.apartment && <><br />{shippingAddress.apartment}</>}
                    <br />
                    {shippingAddress.city}, {shippingAddress.state} {shippingAddress.zipCode}
                    <br />
                    {shippingAddress.country}
                </p>
                <p style={{ fontSize: '16px', lineHeight: '1.5', marginTop: '20px' }}>We&apos;ll send you another email when your order ships. If you have any questions, please don&apos;t hesitate to contact our customer support.</p>
                <p style={{ fontSize: '16px', lineHeight: '1.5', marginTop: '20px', fontWeight: 'bold' }}>Thank you for choosing Lymph!</p>
            </div>
            <div style={{ textAlign: 'center', marginTop: '20px', fontSize: '14px', color: '#666666' }}>
                <p>&copy; 2024 Lymph. All rights reserved.</p>
            </div>
        </div>
    );
};

export default EmailTemplate;