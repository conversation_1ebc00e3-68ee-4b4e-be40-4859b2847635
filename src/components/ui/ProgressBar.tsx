import React from 'react';

interface ProgressBarProps {
    progress: number;
}

const ProgressBar: React.FC<ProgressBarProps> = ({ progress }) => {
    return (
        <div className="w-full bg-gray-200 h-1">
            <div
                className="h-1 transition-all duration-300 ease-in-out"
                style={{
                    width: `${progress}%`,
                    backgroundColor: '#008ab'
                }}
            ></div>
        </div>
    );
};

export default ProgressBar;