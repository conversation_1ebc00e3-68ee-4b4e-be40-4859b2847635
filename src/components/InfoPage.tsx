"use client";
import React from "react";
import { useRouter } from "next/navigation";
import { ArrowRight } from 'lucide-react';
import Image from 'next/image';
import { QuizQuestion } from "@/data/quiz-data";
import Carousel from "@/components/Carousel";
import FullImageCarousel from "./FullImageCarousel";
import CardCarousel from "./CardCarousel";

interface InfoPageProps {
  question: QuizQuestion;
}

export default function InfoPage({ question }: InfoPageProps) {
  const router = useRouter();

  const handleNext = () => {
    router.push(question.nextPage);
  };

  return (
    <div className="flex flex-col min-h-screen bg-white">
      {/* Green header with progress bar, question, subheading */}
      <div className="w-full bg-gradient-to-br from-[#0F403D] via-[#0E6661] to-[#0F403D] rounded-b-3xl px-4 pt-8 pb-10 md:pb-14 text-white relative overflow-hidden flex flex-col items-center">
        {/* Decorative icons */}
        <div className="absolute left-8 top-16 opacity-40 z-0">
          <svg width="32" height="32">
            <circle cx="16" cy="16" r="2" fill="#B6CFC7" />
            <line x1="16" y1="0" x2="16" y2="32" stroke="#B6CFC7" strokeWidth="1.5" />
            <line x1="0" y1="16" x2="32" y2="16" stroke="#B6CFC7" strokeWidth="1.5" />
          </svg>
        </div>
        <div className="absolute right-8 top-10 opacity-40 z-0">
          <svg width="32" height="32">
            <circle cx="16" cy="16" r="2" fill="#B6CFC7" />
            <line x1="16" y1="0" x2="16" y2="32" stroke="#B6CFC7" strokeWidth="1.5" />
            <line x1="0" y1="16" x2="32" y2="16" stroke="#B6CFC7" strokeWidth="1.5" />
          </svg>
        </div>
        {/* Progress bar */}
        <div className="w-full max-w-lg mx-auto mb-6">
          <div className="flex justify-between mb-2">
            <span className="text-sm text-white/70"> </span>
            <span className="text-sm text-white/80 font-medium">{question.progress}%</span>
          </div>
          <div className="h-2 bg-white/20 rounded-full overflow-hidden">
            <div className="h-full bg-[#c6e48b]" style={{ width: `${question.progress}%` }}></div>
          </div>
        </div>
        {/* Question and subheading */}
        <div className="w-full max-w-lg mx-auto text-center z-10">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-3 leading-tight">
            {question.title}
          </h2>
          <p className="text-white/70 text-base md:text-lg mb-2">
            {question.subheading}
          </p>
        </div>
      </div>
      {/* White section with carousel or image/description, and Next button */}
      <div className="flex-1 w-full flex flex-col items-center bg-white px-4 pt-8 pb-16">
        <div className="w-full max-w-lg mx-auto space-y-8">
          {question.cards && question.cards.length > 0 ? (
            <CardCarousel cards={question.cards} />
          ) : question.carousel && question.id === "lymphatic-support" ? (
            <FullImageCarousel slides={question.carousel} />
          ) : question.carousel ? (
            <Carousel slides={question.carousel} />
          ) : (
            <>
              {question.imageUrl && (
                <div className="relative w-full aspect-square">
                  <Image
                    src={question.imageUrl}
                    alt={question.title}
                    fill
                    className="object-contain"
                    priority
                  />
                </div>
              )}
              {question.description && (
                <p className="text-gray-700 text-lg leading-relaxed">
                  {question.description}
                </p>
              )}
            </>
          )}
        </div>
        <button
          className="mt-16 w-full max-w-lg py-4 px-6 rounded-full font-medium transition-all flex items-center justify-center text-lg bg-[#c6e48b] text-[#184C43] hover:bg-[#b6d97a]"
          onClick={handleNext}
        >
          Next
        </button>
      </div>
    </div>
  );
} 