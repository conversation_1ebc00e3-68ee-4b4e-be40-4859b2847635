export interface QuizOption {
  id: string;
  text: string;
  icon?: boolean;
  customIcon?: string;
}

type QuizFlow = 'face' | 'body' | 'face-and-body' | 'common' | 'disabled';

export interface QuizQuestion {
  id: string;
  title: string;
  subheading: string;
  options?: QuizOption[];
  progress: number;
  nextPage: string;
  description?: string;
  imageUrl?: string;
  cardTitle?: string;
  cardSub?: string;
  cardFooter?: string;
  privacy?: string;
  allowMultiple?: boolean;
  bulletPoints?: string[];
  features?: {
    icon: string;
    title: string;
    desc: string;
  }[];
  weekdays?: string[];
  timesOfDay?: {
    value: string;
    label: string;
  }[];
  durations?: number[];
  experts?: {
    name: string;
    desc: string;
  }[];
  optInText?: string;
  newsletterText?: string;
  focusAreas?: {
    title: string;
    areas: string[];
    tags: string[];
  };
  plans?: {
    id: string;
    label: string;
    price: string;
    save: string;
    sub: string;
    best?: boolean;
  }[];
  faqs?: {
    q: string;
    a: string;
  }[];
  reviews?: {
    name: string;
    text: string;
    location: string;
    rating: number;
  }[];
  guarantee?: {
    title: string;
    text: string;
    links: string[];
  };
  backedBy?: {
    title: string;
    text: string;
  };
  finalCta?: {
    title: string;
    text: string;
  };
  carousel?: {
    imageUrl: string;
    alt: string;
    testimonial: string;
    name: string;
    location: string;
    year: string;
    rating: number;
  }[];
  cards?: {
    title: string;
    description: string;
    imageUrl: string;
    backgroundColor?: string;
    textColor?: string;
    subtitle?: string;
    bulletPoints?: string[];
    footer?: string;
    iconUrl?: string;
    [key: string]: any;
  }[];
  flow?: QuizFlow;
}

export const quizData: Record<string, QuizQuestion> = {
  "primary-goal": {
    id: "primary-goal",
    title: "What is your primary goal with improving your face and neck?",
    subheading: "Tell us what you'd like to achieve first.",
    options: [
      { id: "sharpen-jawline", text: "Sharpen jawline & reduce double-chin", icon: true, customIcon: "primary-goal-jawline" },
      { id: "reduce-wrinkles", text: "Reduce wrinkles", icon: true, customIcon: "primary-goal-wrinkles" },
      { id: "smooth-eye-bags", text: "Smooth eye bags", icon: true, customIcon: "primary-goal-eye-bags" },
      { id: "facial-symmetry", text: "Improve facial symmetry", icon: true, customIcon: "primary-goal-symmetry" },
      { id: "tighten-skin", text: "Tighten skin", icon: true, customIcon: "primary-goal-tighten" },
    ],
    progress: 35,
    nextPage: "/quiz/not-alone-face",
    flow: 'face'
  },
  "face-yoga": {
    id: "face-yoga",
    title: "Have you heard about facial lymphatic drainage?",
    subheading: "It's a gentle way to reduce puffiness and trigger your skin's natural detox through stimulation of your lymphatic system.",
    options: [
      { id: "yes", text: "Yes I know everything about it.", icon: true, customIcon: "face-yoga-yes" },
      { id: "know", text: "I know a few things.", icon: true, customIcon: "face-yoga-know" },
      { id: "notSure", text: "Not sure", icon: true, customIcon: "face-yoga-not-sure" },
    ],
    progress: 38,
    nextPage: "/quiz/not-alone-face",
    flow: 'disabled'
  },
  "not-alone-face": {
    id: "not-alone-face",
    title: "You're not Alone!  💪",
    subheading: "25,000+ people have eased swelling and lymphatic discomfort with Lymph.",
    progress: 41,
    nextPage: "/quiz/skin-condition",
    description: "Our community of over 100,000 users has seen remarkable results. From reducing puffiness to achieving a more defined jawline, Lymph's techniques have helped people worldwide achieve their skin goals.",
    carousel: [
      {
        imageUrl: "/images/not-alone-face-carousel1.svg",
        alt: "Before and after of Mandy's face",
        testimonial: "I couldn't believe the difference after just a few sessions. My face looks noticeably more defined and refreshed. I feel lighter and more confident every day.",
        name: "Mandy",
        location: "Pennsylvania",
        year: "2024",
        rating: 5
      },
      {
        imageUrl: "/images/not-alone-face-carousel2.svg",
        alt: "Before and after of Sophia's face",
        testimonial: "After following the routines, my face looks so much more sculpted and refreshed. I feel confident going makeup-free for the first time in years!",
        name: "Sophia",
        location: "Texas",
        year: "2024",
        rating: 4
      },
      {
        imageUrl: "/images/not-alone-face-carousel3.svg",
        alt: "Before and after of Jasmine's face",
        testimonial: "My skin feels so much smoother, and the puffiness has visibly reduced. I feel way more confident in my appearance now!",
        name: "Jasmine",
        location: "New York",
        year: "2024",
        rating: 5
      }
    ],
    flow: 'face'
  },
  "not-alone-body": {
    id: "not-alone-body",
    title: "You're not Alone!  💪",
    subheading: "25,000+ people have eased swelling and lymphatic discomfort with Lymph.",
    progress: 41,
    nextPage: "/quiz/self-care-ritual",
    description: "Our community of over 100,000 users has seen remarkable results. From reducing puffiness to achieving a more defined body, Lymph's techniques have helped people worldwide achieve their goals.",
    carousel: [
      {
        imageUrl: "/images/not-alone-body-carousel1.svg",
        alt: "Before and after of Sarah's body transformation",
        testimonial: "The lymphatic drainage techniques have completely transformed my body. My legs feel lighter, and the swelling has reduced significantly. I can finally wear my favorite clothes with confidence!",
        name: "Sarah",
        location: "California",
        year: "2024",
        rating: 5
      },
      {
        imageUrl: "/images/not-alone-body-carousel2.svg",
        alt: "Before and after of Emma's body transformation",
        testimonial: "After just a few weeks of consistent practice, I noticed a dramatic reduction in water retention. My waist is more defined, and I feel so much more comfortable in my own skin.",
        name: "Emma",
        location: "Florida",
        year: "2024",
        rating: 5
      },
      {
        imageUrl: "/images/not-alone-body-carousel3.svg",
        alt: "Before and after of Rachel's body transformation",
        testimonial: "The results are incredible! My body feels lighter, more toned, and the puffiness has decreased significantly. This has been a game-changer for my confidence and overall well-being.",
        name: "Rachel",
        location: "New York",
        year: "2024",
        rating: 5
      }
    ],
    flow: 'body'
  },
  "skin-condition": {
    id: "skin-condition",
    title: "Are you satisfied with your skin condition?",
    subheading: "Let's start by understanding your current skin concerns.",
    options: [
      { id: "yes", text: "Yes, I just want to keep this result forever", icon: true, customIcon: "skin-condition-yes" },
      { id: "notReally", text: "Not really, I still need some small improvements", icon: true, customIcon: "skin-condition-not-really" },
      { id: "no", text: "No, I have a lot of skin problems", icon: true, customIcon: "skin-condition-no" },
    ],
    progress: 44,
    nextPage: "/quiz/focus",
    allowMultiple: true,
    flow: 'face'
  },
  "focus": {
    id: "focus",
    title: "Choose your focus (select one or more)",
    subheading: "Choose the areas you want to focus on to get the best results.",
    options: [
      { id: "face-sculpting", text: "Face sculpting", icon: true, customIcon: "focus-face-sculpting" },
      { id: "fresh-complexion", text: "Fresh complexion", icon: true, customIcon: "focus-fresh-complexion" },
      { id: "healthy-habits", text: "Healthy habits", icon: true, customIcon: "focus-healthy-habits" },
      { id: "collagen-boost", text: "Collagen boost", icon: true, customIcon: "focus-collagen-boost" },
      { id: "consistent-routine", text: "Consistent routine", icon: true, customIcon: "focus-consistent-routine" },
      { id: "even-skin-tone", text: "Even skin tone", icon: true, customIcon: "focus-even-skin-tone" },
      { id: "blemish-reduction", text: "Blemish reduction", icon: true, customIcon: "focus-blemish-reduction" },
      { id: "mental-health", text: "Mental health", icon: true, customIcon: "focus-mental-health" },
      { id: "double-chin-reduction", text: "Double chin reduction", icon: true, customIcon: "focus-double-chin-reduction" },
      { id: "stress-relief", text: "Stress relief", icon: true, customIcon: "focus-stress-relief" },
      { id: "detox", text: "Detox", icon: true, customIcon: "focus-detox" },
    ],
    progress: 47,
    nextPage: "/quiz/skin-care-routine",
    allowMultiple: true,
    flow: 'face'
  },
  "skin-care-routine": {
    id: "skin-care-routine",
    title: "The truth your skincare routine isn't telling you",
    subheading: "It's more than skin deep.",
    progress: 50,
    nextPage: "/quiz/routine",
    imageUrl: "/images/skin-care-routine.svg",
    flow: 'face'
  },
  "routine": {
    id: "routine",
    title: "How much time can you realistically commit each day to support your lymphatic health?",
    subheading: "Consistency matters more than duration.",
    options: [
      { id: "5-mins", text: "5 mins", icon: true, customIcon: "routine-5-mins" },
      { id: "10-mins", text: "10 mins", icon: true, customIcon: "routine-10-mins" },
      { id: "15-plus-mins", text: "15+ mins", icon: true, customIcon: "routine-15-plus-mins" },
      { id: "not-sure", text: "Not sure yet", icon: true, customIcon: "routine-not-sure-yet" },
    ],
    progress: 53,
    nextPage: "/quiz/self-care-ritual",
    allowMultiple: false,
    flow: 'face'
  },
  "sunscreen": {
    id: "sunscreen",
    title: "Do you put sunscreen on when you go outside?",
    subheading: "We consider your sun care routine when tailoring your face wellness plan.",
    options: [
      { id: "always", text: "Yes, always", icon: true, customIcon: "sunscreen-yes" },
      { id: "sunny", text: "Yes, but only in sunny weather", icon: true, customIcon: "sunscreen-sunny" },
      { id: "sometimes", text: "Sometimes", icon: true, customIcon: "sunscreen-sometimes" },
      { id: "rarely", text: "Rarely", icon: true, customIcon: "sunscreen-rarely" },
      { id: "never", text: "Never", icon: true, customIcon: "sunscreen-never" },
    ],
    progress: 56,
    nextPage: "/quiz/self-care-ritual",
    allowMultiple: false,
    flow: 'disabled'
  },
  "makeup": {
    id: "makeup",
    title: "How often do you wear makeup?",
    subheading: "Your makeup usage influences the care plan we create.",
    options: [
      { id: "always", text: "Yes, always", icon: true, customIcon: "makeup-yes" },
      { id: "occasionally", text: "Occasionally", icon: true, customIcon: "makeup-occasionally" },
      { id: "rarely", text: "Rarely", icon: true, customIcon: "makeup-rarely" },
      { id: "never", text: "Never", icon: true, customIcon: "makeup-never" },
    ],
    progress: 59,
    nextPage: "/quiz/self-care-ritual",
    allowMultiple: false,
    flow: 'disabled'
  },
  "skin-analysis": {
    id: "skin-analysis",
    title: "Analysing your skin..",
    subheading: "Let's analyze your skin's needs.",
    progress: 62,
    nextPage: "/quiz/self-care-ritual",
    description: "We track subtle stress and recovery patterns that quietly impact your skin from within.",
    cardTitle: "We're Tracking What\nYour Skin Feels..",
    cardSub: "We track subtle stress and recovery patterns that quietly impact your skin from within.",
    bulletPoints: [
      "Tracking your skin for precise, personalized insights.",
      "Spotting patterns in your routine.",
      "Analysing & Creating a plan tailored to your skin.",
    ],
    flow: 'disabled'
  },
  "right-track": {
    id: "right-track",
    title: "Your Skin is Already on the\nRight Track ✨",
    subheading: "You've done a great job maintaining your skin — now let's help you preserve those results for the long term.",
    progress: 65,
    nextPage: "/quiz/self-care-ritual",
    cardTitle: "How Lymph Will\nHelp You",
    cardSub: "Based on your consultation, we've created a customized plan for your skin needs",
    features: [
      {
        icon: "personalized",
        title: "Personalized Plan",
        desc: "Custom routines tailored to your specific skin needs and goals"
      },
      {
        icon: "expert",
        title: "Expert Guidance",
        desc: "Video tutorials and step-by-step instructions for each technique"
      },
      {
        icon: "progress",
        title: "Progress Tracking",
        desc: "Regular assessments and adjustments to optimize results"
      }
    ],
    flow: 'disabled'
  },
  "right-hands": {
    id: "right-hands",
    title: "You're in the Right Hands 👐",
    subheading: "Let's put you in the right hands.",
    progress: 68,
    nextPage: "/quiz/self-care-ritual",
    cardTitle: "How Lymph Will\nHelp You",
    cardSub: "Based on your consultation, we've created a customized plan for your skin needs",
    features: [
      {
        icon: "personalized",
        title: "Personalized Plan",
        desc: "Custom routines tailored to your specific skin needs and goals"
      },
      {
        icon: "expert",
        title: "Expert Guidance",
        desc: "Video tutorials and step-by-step instructions for each technique"
      },
      {
        icon: "progress",
        title: "Progress Tracking",
        desc: "Regular assessments and adjustments to optimize results"
      }
    ],
    flow: 'disabled'
  },
  "cosmetologist": {
    id: "cosmetologist",
    title: "How often do you visit a cosmetologist?",
    subheading: "Select the highlighted areas you'd like to tone.",
    progress: 71,
    nextPage: "/quiz/self-care-ritual",
    options: [
      { id: "monthly", text: "Once a month or more", icon: true, customIcon: "cosmetologist-month" },
      { id: "quarterly", text: "Once in several months", icon: true, customIcon: "cosmetologist-months" },
      { id: "yearly", text: "Once a year", icon: true, customIcon: "cosmetologist-year" },
      { id: "never", text: "Never", icon: true, customIcon: "cosmetologist-never" },
    ],
    allowMultiple: false,
    flow: 'disabled'
  },
  "lymphatic-drainage-techniques": {
    id: "lymphatic-drainage-techniques",
    title: "How familiar are you with lymphatic drainage techniques?",
    subheading: "Let us know your experience level to tailor the perfect massage plan for you!",
    progress: 74,
    nextPage: "/quiz/self-care-ritual",
    options: [
      { id: "beginner", text: "Beginner", icon: true, customIcon: "familiar-beginner" },
      { id: "intermediate", text: "Intermediate", icon: true, customIcon: "familiar-intermediate" },
      { id: "advanced", text: "Advanced", icon: true, customIcon: "familiar-advanced" },
    ],
    allowMultiple: false,
    flow: 'disabled'
  },
  "dietary-habits": {
    id: "dietary-habits",
    title: "Dietary habits affecting lymphatic health",
    subheading: "Discover how what you eat can support or hinder your lymphatic system's function.",
    progress: 77,
    nextPage: "/quiz/daily-water-intake",
    options: [
      { id: "salt", text: "High salt intake", icon: true, customIcon: "dietary-habits-salt" },
      { id: "balanced", text: "Balanced diet", icon: true, customIcon: "dietary-habits-balanced" },
      { id: "not-mindful", text: "Not mindful about diet", icon: true, customIcon: "dietary-habits-not-mindful" },
    ],
    allowMultiple: false,
    flow: 'face-and-body'
  },
  "daily-water-intake": {
    id: "daily-water-intake",
    title: "Daily Water Intake",
    subheading: "Hydration plays a key role in maintaining healthy lymph flow—how much water do you drink daily?",
    progress: 80,
    nextPage: "/quiz/medical-concerns",
    options: [
      { id: "0.5", text: "0.5 glass" },
      { id: "1", text: "1 glass" },
      { id: "1.5", text: "1.5 glasses" },
      { id: "2", text: "2 glasses" },
      { id: "2.5", text: "2.5 glasses" },
      { id: "3", text: "3 glasses" },
      { id: "3.5", text: "3.5 glasses" },
      { id: "4", text: "4 glasses" },
      { id: "4.5", text: "4.5 glasses" },
      { id: "5", text: "5 glasses" },
      { id: "5.5", text: "5.5 glasses" },
      { id: "6", text: "6 glasses" },
      { id: "6.5", text: "6.5 glasses" },
      { id: "7", text: "7 glasses" },
      { id: "7.5", text: "7.5 glasses" },
      { id: "8", text: "8 glasses" },
    ],
    allowMultiple: false,
    description: "Select how many glasses of water you drink daily",
    imageUrl: "/images/daily-water-intake-empty.svg",
    flow: 'face-and-body'
  },
  "medical-concerns": {
    id: "medical-concerns",
    title: "Do you have specific medical concerns related to swelling or circulation?",
    subheading: "Let us know if you experience any issues with swelling or circulation that might affect your lymphatic health.",
    progress: 83,
    nextPage: "/quiz/motivation",
    options: [
      { id: "yes", text: "Yes", icon: true, customIcon: "medical-concerns-yes" },
      { id: "no", text: "No", icon: true, customIcon: "medical-concerns-no" },
      { id: "unsure", text: "Unsure", icon: true, customIcon: "medical-concerns-unsure" },
    ],
    allowMultiple: false,
    flow: 'face-and-body'
  },
  "motivation": {
    id: "motivation",
    title: "Motivation",
    subheading: "Select your motivation to guide your lymphatic drainage experience",
    progress: 86,
    nextPage: "/quiz/mental-health",
    options: [
      { id: "confident", text: "I want to look younger to feel confident", icon: true, customIcon: "motivation-younger" },
      { id: "less-cosmetics", text: "I want to use less cosmetics", icon: true, customIcon: "motivation-cosmetics" },
      { id: "impress", text: "I want to impress people around me", icon: true, customIcon: "motivation-impress" },
      { id: "partner", text: "I'm afraid my partner will break up with me", icon: true, customIcon: "motivation-partner" },
      { id: "prevent-aging", text: "I want to prevent aging", icon: true, customIcon: "motivation-aging" },
      { id: "other", text: "Other", icon: true, customIcon: "motivation-other" },
    ],
    allowMultiple: false,
    flow: 'face-and-body'
  },
  "mental-health": {
    id: "mental-health",
    title: "Mental and emotional health",
    subheading: "How would you describe your mental and emotional state on a regular basis?",
    progress: 89,
    nextPage: "/quiz/age",
    options: [
      { id: "relaxed", text: "Relaxed", icon: true, customIcon: "mental-health-relaxed" },
      { id: "stressed", text: "Stressed", icon: true, customIcon: "mental-health-stressed" },
      { id: "anxious", text: "Anxious", icon: true, customIcon: "mental-health-anxious" },
      { id: "depressed", text: "Depressed", icon: true, customIcon: "mental-health-depressed" },
      { id: "prefer-not-to-say", text: "Prefer not to say", icon: true, customIcon: "mental-health-not-to-say" },
    ],
    allowMultiple: false,
    flow: 'face-and-body'
  },
  "age": {
    id: "age",
    title: "What's your age?",
    subheading: "Select your age range to help us tailor your lymphatic drainage plan.",
    progress: 92,
    nextPage: "/quiz/self-care-ritual",
    options: [
      { id: "18-29", text: "18-29", icon: true, customIcon: "age-twenty-nine" },
      { id: "30-39", text: "30-39", icon: true, customIcon: "age-thirty-nine" },
      { id: "40-49", text: "40-49", icon: true, customIcon: "age-forty-nine" },
      { id: "50-59", text: "50-59", icon: true, customIcon: "age-fifty-nine" },
      { id: "60-69", text: "60-69", icon: true, customIcon: "age-sixty-nine" },
    ],
    allowMultiple: false,
    flow: 'face-and-body'
  },
  "current-lifestyle": {
    id: "current-lifestyle",
    title: "Creating plan on the basis of\nyour current lifestyle..",
    subheading: "Let's understand your current lifestyle to create a personalized plan.\nWe'll help you build sustainable habits that work for you.",
    progress: 95,
    nextPage: "/quiz/self-care-ritual",
    cardTitle: "We're Understanding\nHow You Live…",
    cardSub: "Connecting your daily life to your skin's wellness!",
    bulletPoints: [
      "Mapping Lifestyle Impact on Skin Health",
      "Spotting patterns in your routine.",
      "Analysing & Creating a plan tailored to your skin.",
    ],
    flow: 'disabled'
  },
  "self-care-ritual": {
    id: "self-care-ritual",
    title: "When is the best time for your daily\nself-care ritual?",
    subheading: "Let's create your perfect self-care ritual.",
    progress: 98,
    nextPage: "/quiz/rhythm",
    weekdays: ["MON", "TUE", "WED", "THU", "FRI", "SAT", "SUN"],
    timesOfDay: [
      { value: "morning", label: "In the morning" },
      { value: "evening", label: "In the evening" },
      { value: "night", label: "At night" },
    ],
    durations: [10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 60],
    flow: 'common'
  },
  "rhythm": {
    id: "rhythm",
    title: "Lymph fits right into\nyour rhythm",
    subheading: "Let's find your perfect rhythm.",
    progress: 100,
    nextPage: "/quiz/science-backed",
    cardTitle: "Morning is the magic hour! 🌟",
    cardSub: "80% of users prefer to start their morning with Lymph.",
    cardFooter: "Join the morning ritual that delivers real results.",
    flow: 'common'
  },
  "science-backed": {
    id: "science-backed",
    title: "Science-backed methods Expert-developed care",
    subheading: "Trusted expertise shaping every step of your detox and self-care journey.",
    progress: 100,
    nextPage: "/quiz/clinical-practice",
    cardTitle: "Detox your skin with\nscience and self-care 🧪",
    cardSub: "Harvard Medical School reports that face exercises also help improve muscle tone in the face and could help with gravity-related fat loss or redistribution on the face.",
    cardFooter: "Dermatologists at Northwestern University report that face yoga has been proven to make you look 3 years younger in 20 weeks.",
    flow: 'common'
  },
  "clinical-practice": {
    id: "clinical-practice",
    title: "Inspired by clinical practice. Built by professionals.",
    subheading: "Trusted expertise shaping every step of your detox and self-care journey.",
    progress: 100,
    nextPage: "/quiz/plan",
    experts: [
      {
        name: "Dr. Melissa Gallagher, ND",
        desc: "Board-certified Naturopathic Doctor with 15+ years' experience in lymphatic therapy and holistic detox. Founder of a wellness clinic."
      },
      {
        name: "Lana Farson",
        desc: "Licensed Massage Therapist & Certified Lymphedema Therapist with 10+ years treating post-op swelling and fluid retention."
      },
      {
        name: "Jillian Osborne",
        desc: "Jillian Osborne, a Face Yoga expert with over 15 years of experience, empowers women through certified facial fitness programs. She leads Fresh Face Pilates in Vancouver."
      }
    ],
    flow: 'common'
  },
  "plan": {
    id: "plan",
    title: "Your Plan is here ⭐",
    subheading: "Let's create your personalized plan.",
    progress: 100,
    nextPage: "/quiz/offer",
    cardTitle: "Claim your plan",
    cardSub: "Enter your email to receive it.",
    cardFooter: "We'll send your results to your email.",
    optInText: "Opt in for wellness tips, free bonuses, and app updates",
    flow: 'common'
  },
  "offer": {
    id: "offer",
    title: "Your Plan is here",
    subheading: "Let's Begin Your Skin Ritual!",
    progress: 100,
    nextPage: "/quiz/thank-you",
    focusAreas: {
      title: "Focus Areas",
      areas: ["Face & Neck", "Body areas"],
      tags: ["Reduce Puffiness", "Feel Lighter", "De-stress", "Neck Sculpting", "Cheek Lift", "Dark Circle"]
    },
    plans: [
      { id: "7day", label: "7 Day", price: "$29", save: "Save 30%", sub: "$42 /month" },
      { id: "3month", label: "3 Months Plan", price: "$24", save: "Save 40%", sub: "$42 /month", best: true },
      { id: "6month", label: "6 Months Plan", price: "$32", save: "Save 56%", sub: "$42 /month" }
    ],
    features: [
      { icon: "[svg]", title: "Lymphatic Self Massage Videos", desc: "Step-by-step guided routines." },
      { icon: "[svg]", title: "Daily Detox Tips & Tracker", desc: "Hydration goals & detox, tailored to you." },
      { icon: "[svg]", title: "Face Yoga", desc: "De-puffing routines & exercises." },
      { icon: "[svg]", title: "Stress Relief Guides", desc: "Emotional balance techniques." }
    ],
    faqs: [
      {
        q: "How soon will I see results with Lymph?",
        a: "Most users see visible improvements within 2-3 weeks of consistent use, with best results after 4+ weeks."
      },
      {
        q: "What makes Lymph different from other skincare routines?",
        a: "Lymph goes beyond surface-level skincare. It's a science-backed, holistic system that addresses the root causes of puffiness and dull skin, giving real results you both see and feel."
      },
      {
        q: "Is Lymph safe for all skin types?",
        a: "Yes, Lymph is designed to be gentle and effective for all skin types, including sensitive skin."
      },
      {
        q: "Can I use Lymph if I'm already seeing a dermatologist or professional?",
        a: "Absolutely! Lymph can complement professional care, but always consult your provider if you have concerns."
      },
      {
        q: "Do I need any special tools or devices to follow the plan?",
        a: "No special tools are required. All routines can be done with your hands and household items."
      }
    ],
    reviews: [
      {
        name: "Mandy",
        text: "The guided routines helped me control swelling in my legs naturally. I finally feel lighter and more confident.",
        location: "Florida, 2024",
        rating: 5
      },
      {
        name: "Alicia",
        text: "I noticed a big change in the very first session. The discomfort in my legs is almost gone, and I feel more active throughout the day.",
        location: "New York, 2023",
        rating: 4
      },
      {
        name: "Rebecca",
        text: "This has been a game-changer. My double chin looks more toned, and I feel more confident in photos.",
        location: "Texas, 2024",
        rating: 4
      },
      {
        name: "Emmet",
        text: "I was surprised at how effective the routines were. The swelling is down, and I feel more confident wearing shorts again.",
        location: "California, 2025",
        rating: 5
      }
    ],
    guarantee: {
      title: "100% Money-Back Guarantee",
      text: "Not satisfied? Get a full refund within 30 days, no questions asked.",
      links: ["Privacy Policy", "24/7 support"]
    },
    backedBy: {
      title: "Women'sHealth",
      text: "experts, trusted by more than 25,000+ users—lymph is transforming results, one body at a time."
    },
    finalCta: {
      title: "Don't Wait for perfect skin —\nIt's already waiting for you.",
      text: "Claim your plan now before spots get all used up!"
    },
    flow: 'common'
  },
  "thank-you": {
    id: "thank-you",
    title: "Thank You! ✨",
    subheading: "Your personalized plan is on its way.",
    progress: 100,
    nextPage: "/dashboard",
    cardTitle: "Check Your Email",
    cardSub: "We've sent your complete Lymph plan to your email address.",
    bulletPoints: [
      "Your personalized plan will arrive shortly",
      "Download the Lymph app to get started",
      "Join our community of 100,000+ users"
    ],
    flow: 'common'
  },
  "lymphatic-drainage": {
    id: "lymphatic-drainage",
    title: "Have you heard about Lymphatic Drainage before?",
    subheading: "Answer a few quick questions to get your personalised routine.",
    options: [
      { id: "yes", text: "Yes", icon: true, customIcon: "lymphatic-drainage-yes" },
      { id: "no", text: "No", icon: true, customIcon: "lymphatic-drainage-no" },
      { id: "sort-of", text: "Sort of", icon: true, customIcon: "lymphatic-drainage-sort-of" },
    ],
    progress: 3,
    nextPage: "/quiz/lymphatic-support",
    flow: 'face'
  },
  "lymphatic-support": {
    id: "lymphatic-support",
    title: "Why Your Skin Needs\nLymphatic Support",
    subheading: "Unlock radiant, healthy skin by boosting your body's natural detox system.",
    progress: 5,
    nextPage: "/quiz/gender",
    cards: [
      {
        title: "What is Lymphatic Drainage?",
        description: "A gentle therapy that stimulates the lymphatic system to reduce swelling, boost immunity, and promote overall wellness.",
        imageUrl: "/images/lymphatic-support-girl.svg",
        backgroundColor: "#FFF5E5",
        textColor: "#B85C38",
        iconUrl: "/images/lymphatic-support-card1-icon.svg"
      },
      {
        title: "Gentle Detox for Your Body",
        description: "A gentle, rhythmic therapy that boosts your lymphatic system—reducing swelling, flushing out toxins, and leaving you lighter, healthier, and deeply relaxed.",
        imageUrl: "/images/lymphatic-support-detox.svg",
        backgroundColor: "#FFF3F0",
        textColor: "#B85C38",
        iconUrl: "/images/lymphatic-support-card2-icon.svg"
      },
      {
        title: "Backed by Science",
        description: "Clinically supported therapy that enhances lymph flow, reduces inflammation, and supports immune health—backed by science for real, lasting benefits.",
        imageUrl: "/images/lymphatic-support-science.svg",
        backgroundColor: "#F1FBF4",
        textColor: "#184C43",
        iconUrl: "/images/lymphatic-support-card3-icon.svg"
      }
    ],
    flow: 'face'
  },
  "health-concerns": {
    id: "health-concerns",
    title: "A growing number of healthcare professionals are recommending Lymph for lymphatic drainage - were you sent here by a healthcare professional?",
    subheading: "We're trusted by experts—let's personalize your experience",
    options: [
      { id: "doctor", text: "Yes, my doctor or specialist recommended it", icon: true, customIcon: "health-concerns-doctor" },
      { id: "massage", text: "Yes, my massage therapist recommended it", icon: true, customIcon: "health-concerns-massage" },
      { id: "self", text: "I discovered it myself", icon: true, customIcon: "health-concerns-I" },
      { id: "friend", text: "A friend or social media introduced me", icon: true, customIcon: "health-concerns-friend" },
      { id: "other", text: "Other", icon: true, customIcon: "health-concerns-other" },
    ],
    progress: 8,
    nextPage: "/quiz/trusted",
    flow: 'disabled'
  },
  "trusted": {
    id: "trusted",
    title: "Thanks for\nletting us know!",
    subheading: "",
    progress: 11,
    nextPage: "/quiz/focus-areas",
    cardTitle: "Lymph is Trusted by Experts,\nProven by Results ✨",
    description: "Over 85% of our users were guided here by a trusted healthcare professional. From doctors to massage therapists, Lymph is becoming essential for lymphatic health.",
    imageUrl: "/images/trusted.svg",
    flow: 'disabled'
  },
  "referral": {
    id: "referral",
    title: "Would you like to share the email of the person who referred you?",
    subheading: "Lymph is Trusted by Experts, Proven by Results",
    progress: 14,
    nextPage: "/quiz/referralname",
    cardTitle: "Enter the email:",
    cardSub: "Let us know who guided you — they deserve the credit.",
    privacy: "100% private. We don't share your details with the person that referred you - your data is safe with Lymph",
    flow: 'disabled'
  },
  "referralname": {
    id: "referralname",
    title: "Would you like to share the name of the person who referred you?",
    subheading: "Lymph is Trusted by Experts, Proven by Results",
    progress: 17,
    nextPage: "/quiz/gender",
    cardTitle: "Enter the name:",
    cardSub: "Let us know who guided you — they deserve the credit.",
    privacy: "100% private. We don't share your details with the person that referred you - your data is safe with Lymph",
    flow: 'disabled'
  },
  "gender": {
    id: "gender",
    title: "What is your biological gender?",
    subheading: "We celebrate you — tell us how you shine.",
    options: [
      { id: "female", text: "Female", icon: true, customIcon: "gender-female" },
      { id: "male", text: "Male", icon: true, customIcon: "gender-male" },
    ],
    progress: 20,
    nextPage: "/quiz/health-concerns",
    flow: 'face'
  },
  "focus-areas": {
    id: "focus-areas",
    title: "Select your main focus areas (choose multiple if applicable)",
    subheading: "Select the highlighted areas you'd like to tone.",
    options: [
      { id: "face-and-neck", text: "Face & Neck" },
      { id: "stomach", text: "Stomach" },
      { id: "legs", text: "Legs" },
      { id: "glutes", text: "Glutes" }, 
      { id: "feet", text: "Feet" }
    ],
    progress: 23,
    nextPage: "/quiz/primary-goal",
    allowMultiple: true,
    flow: 'face'
  },
  "swelling": {
    id: "swelling",
    title: "Do you experience swelling or heaviness there?",
    subheading: "These signs may point to lymphatic buildup. Let's clear the way.",
    options: [
      { id: "frequent", text: "Yes, Frequently", icon: true, customIcon: "swelling-yes" },
      { id: "occasional", text: "Occasionally", icon: true, customIcon: "swelling-occassionally" },
      { id: "rare", text: "Rarely", icon: true, customIcon: "swelling-rarely" },
      { id: "never", text: "Never", icon: true, customIcon: "swelling-never" },
    ],
    progress: 26,
    nextPage: "/quiz/circulation",
    flow: 'body'
  },
  "familiar": {
    id: "familiar",
    title: "How familiar are you with lymphatic-drainage massage techniques?",
    subheading: "A gentle practice with powerful results — and easier than you think.",
    options: [
      { id: "beginner", text: "Beginner", icon: true, customIcon: "familiar-beginner" },
      { id: "intermediate", text: "Intermediate", icon: true, customIcon: "familiar-intermediate" },
      { id: "advanced", text: "Advanced", icon: true, customIcon: "familiar-advanced" },
    ],
    progress: 29,
    nextPage: "/quiz/self-care-ritual",
    flow: 'disabled'
  },
  "circulation": {
    id: "circulation",
    title: "Have you experienced any circulation, oedema, or fluid build up issues?",
    subheading: "These are early signs. The good news? They're easy to address.",
    options: [
      { id: "yes", text: "Yes", icon: true, customIcon: "circulation-yes" },
      { id: "no", text: "No", icon: true, customIcon: "circulation-no" },
      { id: "unsure", text: "Unsure", icon: true, customIcon: "circulation-unsure" },
    ],
    progress: 32,
    nextPage: "/quiz/not-alone-body",
    flow: 'body'
  },
  "facial-analysis": {
    id: "facial-analysis",
    title: "Would you like to do facial analysis for a more personalized report?",
    subheading: "Tap to do a facial analysis.",
    progress: 50,
    nextPage: "/quiz/routine",
    imageUrl: "/images/facial-analysis.svg",
    flow: 'disabled'
  },
  // Add other quiz questions here
}; 