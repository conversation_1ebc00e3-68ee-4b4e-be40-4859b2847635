'use client'
import React, { useState, Suspense, useEffect } from 'react';
import dynamic from 'next/dynamic';
import { useRouter } from 'next/navigation';
import { useAppSelector } from '@/app/redux/hooks';

interface ShippingData {
    firstName: string;
    lastName: string;
    email: string;
    streetAddress: string;
    apartment?: string;
    city: string;
    state: string;
    zipCode: string;
    country: string;
}

const DynamicCheckoutContent = dynamic(() => import('@/components/Checkout/CheckoutContent'), {
    ssr: false,
});

const CheckoutPage: React.FC = () => {
    const router = useRouter();
    const [activeTab, setActiveTab] = useState('shipping');
    const [shippingData, setShippingData] = useState<ShippingData | null>(null);
    const order = useAppSelector(state => state.order);

    useEffect(() => {
        if (typeof window !== 'undefined') {
            const isInitialLoad = sessionStorage.getItem('checkoutInitialized');

            if (!isInitialLoad) {

                sessionStorage.setItem('checkoutInitialized', 'true');
            } else {

                sessionStorage.removeItem('checkoutInitialized');
                router.push('/plans');
            }
        }


        return () => {
            if (typeof window !== 'undefined') {
                sessionStorage.removeItem('checkoutInitialized');
            }
        };
    }, [router]);

    useEffect(() => {
        if (!order) {
            router.push('/plans');
        }
    }, [order, router]);

    const handleContinueToPayment = (data: ShippingData) => {
        setShippingData(data);
        setActiveTab('payment');
    };

    const handlePaymentSuccess = () => {
        setActiveTab('receipt');
    };

    return (
        <div className="container mx-auto p-4 md:p-8 lg:p-12 w-full md:w-4/5 lg:w-3/5 xl:w-2/3">
            <h1 className="font-recoleta text-4xl mb-8 text-center text-black">Checkout</h1>

            <div className="flex justify-center items-center space-x-4 mb-8 text-lg font-recoleta">
                <span className={`${activeTab === 'shipping' ? 'text-black' : 'text-gray-400'}`}>Shipping</span>
                <span className="text-gray-400">&gt;</span>
                <span className={`${activeTab === 'payment' ? 'text-black' : 'text-gray-400'}`}>Payment</span>
                <span className="text-gray-400">&gt;</span>
                <span className={`${activeTab === 'receipt' ? 'text-black' : 'text-gray-400'}`}>Receipt</span>
            </div>

            <div className="bg-white rounded-lg p-2 md:p-8 shadow-lg">
                <Suspense fallback={<div>Loading...</div>}>
                    <DynamicCheckoutContent
                        activeTab={activeTab}
                        shippingData={shippingData}
                        onContinueToPayment={handleContinueToPayment}
                        onPaymentSuccess={handlePaymentSuccess}
                    />
                </Suspense>
            </div>
        </div>
    );
};

export default CheckoutPage;