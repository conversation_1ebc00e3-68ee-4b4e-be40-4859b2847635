"use client";

import { useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";

export default function ThankYouRedirectPage() {
  const router = useRouter();
  const searchParams = useSearchParams();

  useEffect(() => {
    // Preserve query parameters when redirecting
    const params = searchParams?.toString();
    const redirectUrl = params
      ? `/quiz/thank-you?${params}`
      : "/quiz/thank-you";

    console.log("🔄 Redirecting from /thank-you to /quiz/thank-you");
    router.replace(redirectUrl);
  }, [router, searchParams]);

  return (
    <div className="min-h-screen bg-white flex items-center justify-center">
      <div className="text-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#184C43] mx-auto mb-4"></div>
        <p className="text-[#184C43]">Redirecting...</p>
      </div>
    </div>
  );
}
