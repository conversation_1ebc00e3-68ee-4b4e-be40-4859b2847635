import { NextRequest, NextResponse } from 'next/server';
import { getPayPalAccessToken, PAYPAL_API_BASE } from '@/utils/paypalToken';

export async function POST(req: NextRequest) {
    try {
       
        const accessToken = await getPayPalAccessToken();
        const body = await req.json();
        if (!body.product_id || !body.name || !body.billing_cycles) {
            return NextResponse.json(
                { message: 'product_id, name, and billing_cycles are required' },
                { status: 400 }
            );
        }

        const response = await fetch(`${PAYPAL_API_BASE}/v1/billing/plans`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${accessToken}`,
                'Content-Type': 'application/json',
                'Accept': 'application/json',
                'PayPal-Request-Id': `PLAN-${Date.now()}`, 
                'Prefer': 'return=representation'
            },
            body: JSON.stringify({
                product_id: body.product_id,
                name: body.name,
                description: body.description,
                status: body.status || 'ACTIVE',
                billing_cycles: body.billing_cycles,
                payment_preferences: body.payment_preferences,
                taxes: body.taxes
            })
        });

        const responseData = await response.json();

        if (!response.ok) {
            console.error('PayPal API error:', responseData);
            return NextResponse.json(
                {
                    message: 'PayPal API request failed',
                    details: responseData.details || responseData.message
                },
                { status: response.status }
            );
        }

        return NextResponse.json(responseData);
    } catch (error) {
        console.error('PayPal plan creation error:', error);
        return NextResponse.json(
            {
                message: 'Error creating PayPal plan',
                error: error instanceof Error ? error.message : 'Unknown error'
            },
            { status: 500 }
        );
    }
}