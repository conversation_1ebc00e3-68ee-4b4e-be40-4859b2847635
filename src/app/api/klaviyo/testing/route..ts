import type { NextApiRequest, NextApiResponse } from 'next';
import axios from 'axios';

export default async function handler(
    req: NextApiRequest,
    res: NextApiResponse
) {
    if (req.method !== 'POST') {
        return res.status(405).json({ error: 'Method not allowed' });
    }

    const API_KEY = process.env.KLAVIYO_PRIVATE_API; 

    try {
        const response = await axios.post(
            'https://a.klaviyo.com/api/lists/',
            {
                data: {
                    type: 'list',
                    attributes: {
                        name: 'THIS IS REQUIRED', // Change this to dynamic data if needed
                    },
                },
            },
            {
                headers: {
                    Authorization: `Klaviyo-API-Key ${API_KEY}`,
                    'Content-Type': 'application/json',
                    Accept: 'application/json',
                    Revision: '2023-02-22',
                },
            }
        );

        res.status(200).json(response.data);
    } catch (error: any) {
        console.error(error);
        res
            .status(error.response?.status || 500)
            .json({ error: error.response?.data || 'Something went wrong' });
    }
}
