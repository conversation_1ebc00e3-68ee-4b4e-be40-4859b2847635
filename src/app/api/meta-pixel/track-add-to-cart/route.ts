import { NextResponse } from "next/server";
import axios, { AxiosError } from "axios";
import dns from "dns";
import { promisify } from "util";
import crypto from "crypto";

const dnsLookup = promisify(dns.lookup);

function hashData(data: string): string {
  return crypto
    .createHash("sha256")
    .update(data.toLowerCase().trim())
    .digest("hex");
}

async function axiosWithRetry(config: any, retries = 3, delay = 2000) {
  try {
    return await axios(config);
  } catch (error) {
    if (retries > 0 && axios.isAxiosError(error)) {
      const axiosError = error as AxiosError;
      if (axiosError.code === "EAI_AGAIN") {
        dns.setServers(dns.getServers());
      }
      console.log(`Retrying request. Attempts left: ${retries - 1}`);
      await new Promise((resolve) => setTimeout(resolve, delay));
      return axiosWithRetry(config, retries - 1, delay);
    }
    throw error;
  }
}

export async function POST(request: Request) {
  const { data } = await request.json();
  const pixelId = data.pixelId || process.env.NEXT_PUBLIC_FACEBOOK_PIXEL_ID;
  const accessToken = process.env.FACEBOOK_ACCESS_TOKEN;

  console.log("🛍️ [BACKEND] AddToCart API - Received data:", {
    email: data.email ? "[REDACTED]" : "none",
    pixelId,
    contentIds: data.contentIds,
    value: data.value,
    currency: data.currency,
    fbp: data.fbp ? "[PRESENT]" : "none",
    fbc: data.fbc ? "[PRESENT]" : "none",
    fbclid: data.fbclid ? "[PRESENT]" : "none",
  });

  if (!accessToken || !pixelId) {
    console.error("❌ Missing Facebook Pixel ID or Access Token");
    return NextResponse.json(
      { error: "Missing Facebook Pixel ID or Access Token" },
      { status: 500 }
    );
  }

  try {
    await dnsLookup("graph.facebook.com");

    // Enhanced IP detection
    let ipAddress =
      request.headers.get("x-forwarded-for") ||
      request.headers.get("x-real-ip") ||
      request.headers.get("cf-connecting-ip") || // Cloudflare
      request.headers.get("true-client-ip") || // Akamai and Cloudflare
      "0.0.0.0";

    // Handle localhost/development environment
    if (
      ipAddress === "::1" ||
      ipAddress === "127.0.0.1" ||
      ipAddress === "0.0.0.0"
    ) {
      console.log(
        "🏠 Development environment detected, using fallback IP for testing"
      );
      ipAddress = "***************";
    }

    console.log("🌐 [BACKEND] IP address detection:", {
      original: request.headers.get("x-forwarded-for"),
      used: ipAddress,
    });

    const metaPayload = {
      data: [
        {
          event_name: "AddToCart",
          event_time: Math.floor(Date.now() / 1000),
          user_data: {
            client_ip_address: ipAddress,
            client_user_agent: request.headers.get("user-agent"),
            em: data.email ? hashData(data.email) : undefined,
            fbp: data.fbp,
            fbc: data.fbc,
            external_id: data.externalId
              ? hashData(data.externalId)
              : undefined,
            ph: data.phone ? hashData(data.phone) : undefined,
            fn: data.firstName ? hashData(data.firstName) : undefined,
            ln: data.lastName ? hashData(data.lastName) : undefined,
            ct: data.city ? hashData(data.city) : undefined,
            country: data.country ? hashData(data.country) : undefined,
            st: data.state ? hashData(data.state) : undefined,
            zp: data.zip ? hashData(data.zip) : undefined,
            db: data.dateOfBirth ? hashData(data.dateOfBirth) : undefined,
            click_id: data.fbclid || undefined,
          },
          custom_data: {
            content_ids: data.contentIds || ["LymphDrainage"],
            content_type: data.contentType || "subscription",
            currency: data.currency || "USD",
            value: data.value || 29.99,
          },
          action_source: "website",
        },
      ],
    };

    console.log(
      "📤 [BACKEND] Sending AddToCart event to Meta Conversions API:",
      JSON.stringify(metaPayload, null, 2)
    );

    const response = await axiosWithRetry({
      method: "post",
      url: `https://graph.facebook.com/v20.0/${pixelId}/events`,
      data: metaPayload,
      params: {
        access_token: accessToken,
      },
      timeout: 10000,
    });

    console.log(
      "✅ [BACKEND] AddToCart event response from Meta:",
      response.data
    );
    return NextResponse.json({ success: true, data: response.data });
  } catch (error) {
    console.error("❌ [BACKEND] Error tracking AddToCart event:", error);
    if (axios.isAxiosError(error)) {
      const axiosError = error as AxiosError;
      console.error("❌ [BACKEND] Response data:", axiosError.response?.data);
      return NextResponse.json(
        {
          error: "Failed to track AddToCart event",
          details: axiosError.message,
          response: axiosError.response?.data,
        },
        { status: 500 }
      );
    }
    return NextResponse.json(
      { error: "Failed to track AddToCart event", details: "Unknown error" },
      { status: 500 }
    );
  }
}
