import { NextRequest, NextResponse } from 'next/server';
import { getPayPalAccessToken, PAYPAL_API_BASE } from '@/utils/paypalToken';

export async function POST(req: NextRequest) {
    try {
     
        const accessToken = await getPayPalAccessToken();

        const body = await req.json();

        if (!body.name || !body.type) {
            return NextResponse.json(
                { message: 'name and type are required fields' },
                { status: 400 }
            );
        }

        const response = await fetch(`${PAYPAL_API_BASE}/v1/catalogs/products`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${accessToken}`,
                'Content-Type': 'application/json',
                'Accept': 'application/json',
                'PayPal-Request-Id': `PRODUCT-${Date.now()}`,
                'Prefer': 'return=representation'
            },
            body: JSON.stringify({
                name: body.name,
                description: body.description,
                type: body.type,
                category: body.category,
                image_url: body.image_url,
                home_url: body.home_url
            })
        });

        const responseData = await response.json();

        if (!response.ok) {
            console.error('PayPal API error:', responseData);
            return NextResponse.json(
                {
                    message: 'PayPal API request failed',
                    details: responseData.details || responseData.message
                },
                { status: response.status }
            );
        }

        return NextResponse.json(responseData);
    } catch (error) {
        console.error('PayPal product creation error:', error);
        return NextResponse.json(
            {
                message: 'Error creating PayPal product',
                error: error instanceof Error ? error.message : 'Unknown error'
            },
            { status: 500 }
        );
    }
}