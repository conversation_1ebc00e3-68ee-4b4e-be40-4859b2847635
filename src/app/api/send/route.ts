import { EmailTemplate } from '@/components/ui/email-template';
import { Resend } from 'resend';

export async function POST(request: Request) {
    const apiKey = process.env.RESEND_API_KEY;
    if (!apiKey) {
        // Don't throw at build time, just return error at runtime
        return Response.json({ error: 'Missing RESEND_API_KEY' }, { status: 500 });
    }
    const resend = new Resend(apiKey);

    try {
        const { firstName, email, productName, shippingAddress, totalAmount } = await request.json();

        const { data, error } = await resend.emails.send({
            from: 'Lymph <<EMAIL>>',
            to: [email],
            subject: "Thank you for your Lymph purchase!",
            react: EmailTemplate({ firstName, productName, shippingAddress, totalAmount }),
        });

        if (error) {
            return Response.json({ error }, { status: 400 });
        }

        return Response.json({ data });
    } catch (error) {
        return Response.json({ error: 'Internal Server Error' }, { status: 500 });
    }
}
