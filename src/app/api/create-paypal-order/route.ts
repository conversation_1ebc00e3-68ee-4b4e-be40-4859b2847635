import { NextRequest, NextResponse } from 'next/server';
import { getPayPalAccessToken, PAYPAL_API_BASE } from '@/utils/paypalToken';

export async function POST(req: NextRequest) {
    try {
       
        const body = await req.json();

       
        if (!body.purchase_units || !body.purchase_units.length) {
            return new NextResponse(
                JSON.stringify({ message: 'Invalid request body: purchase_units is required' }),
                { status: 400 }
            );
        }

      
        const accessToken = await getPayPalAccessToken();

       
        const response = await fetch(`${PAYPAL_API_BASE}/v2/checkout/orders`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${accessToken}`,
                'Content-Type': 'application/json',
                'Prefer': 'return=representation'
            },
            body: JSON.stringify({
                intent: body.intent,
                purchase_units: body.purchase_units,
                application_context: body.application_context
            })
        });

        if (!response.ok) {
            const errorData = await response.json();
            console.error('PayPal API Error:', errorData);
            throw new Error('PayPal API Error: ' + JSON.stringify(errorData));
        }

        const order = await response.json();

      
        console.log('PayPal Order Created:', {
            orderId: order.id,
            status: order.status,
            links: order.links
        });

       

        return NextResponse.json(order);
    } catch (error: any) {
        console.error('Error in create-paypal-order:', error);
        return new NextResponse(
            JSON.stringify({
                message: 'Error creating PayPal order',
                details: error.message
            }),
            { status: 500 }
        );
    }
}

export async function GET() {
    return new NextResponse(
        JSON.stringify({ message: 'This endpoint only accepts POST requests' }),
        { status: 405 }
    );
}