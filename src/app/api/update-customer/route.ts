import { NextRequest, NextResponse } from 'next/server';
import <PERSON><PERSON> from 'stripe';

const stripe = new Stripe(
  process.env.NODE_ENV === 'production'
    ? process.env.STRIPE_SECRET_KEY_LIVE!
    : process.env.STRIPE_SECRET_KEY_TEST!,
  {
    apiVersion: '2024-06-20',
  }
);

export async function POST(request: NextRequest) {
    try {
        const { customerId, shippingDetails } = await request.json();

        const updatedCustomer = await stripe.customers.update(customerId, {
            metadata: {
                shippingAddress: JSON.stringify(shippingDetails),
            },
        });

        return NextResponse.json({
            code: 'customer_updated',
            customer: updatedCustomer,
        });
    } catch (error) {
        console.error("Error updating customer:", error);
        return NextResponse.json(
            { code: 'customer_update_failed', error },
            { status: 400 }
        );
    }
}