import { NextRequest, NextResponse } from 'next/server';
import { getPayPalAccessToken, PAYPAL_API_BASE } from '@/utils/paypalToken';

export async function POST(req: NextRequest) {
    try {
       
        const accessToken = await getPayPalAccessToken();

        const body = await req.json();

        
        if (!body.plan_id) {
            return NextResponse.json(
                { message: 'plan_id is required' },
                { status: 400 }
            );
        }

        const response = await fetch(`${PAYPAL_API_BASE}/v1/billing/subscriptions`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${accessToken}`,
                'Content-Type': 'application/json',
                'Accept': 'application/json',
                'PayPal-Request-Id': `SUBSCRIPTION-${Date.now()}`,
                'Prefer': 'return=representation'
            },
            body: JSON.stringify({
                plan_id: body.plan_id,
                start_time: body.start_time,
                quantity: body.quantity,
                shipping_amount: body.shipping_amount,
                subscriber: body.subscriber,
                application_context: {
                    ...body.application_context,
                    return_url: body.application_context.return_url,
                    cancel_url: body.application_context.cancel_url
                }
            })
        });

        const responseData = await response.json();

        if (!response.ok) {
            console.error('PayPal API error:', responseData);
            return NextResponse.json(
                {
                    message: 'PayPal API request failed',
                    details: responseData.details || responseData.message
                },
                { status: response.status }
            );
        }

        return NextResponse.json(responseData);
    } catch (error) {
        console.error('PayPal subscription error:', error);
        return NextResponse.json(
            {
                message: 'Error creating PayPal subscription',
                error: error instanceof Error ? error.message : 'Unknown error'
            },
            { status: 500 }
        );
    }
}