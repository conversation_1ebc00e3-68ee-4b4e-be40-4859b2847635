import { NextRequest, NextResponse } from "next/server";
import <PERSON><PERSON> from "stripe";

const stripe = new Stripe(
  process.env.NODE_ENV === 'production'
    ? process.env.STRIPE_SECRET_KEY_LIVE!
    : process.env.STRIPE_SECRET_KEY_TEST!,
  {
    apiVersion: '2024-06-20',
  }
);

const FIRST_TIME_DISCOUNT_PERCENT = 10;

export async function POST(request: NextRequest) {
  try {
    const { customerId, priceId } = await request.json();
    console.log(`Received request for subscription - customerId: ${customerId}, priceId: ${priceId}`);

    // Check if this is the customer's first subscription
    const existingSubscriptions = await stripe.subscriptions.list({
      customer: customerId,
      limit: 1,
    });

    const isFirstSubscription = existingSubscriptions.data.length === 0;
    console.log(`Is first subscription: ${isFirstSubscription}`);

    let subscription;
    if (isFirstSubscription) {
      console.log(`Creating coupon for first-time subscriber`);
      // Create a Coupon for 10% off
      const coupon = await stripe.coupons.create({
        percent_off: FIRST_TIME_DISCOUNT_PERCENT,
        duration: 'once',
      });
      console.log(`Coupon created: ${coupon.id}`);

      // Create the subscription with the coupon
      subscription = await stripe.subscriptions.create({
        customer: customerId,
        items: [{ price: priceId }],
        payment_behavior: "default_incomplete",
        expand: ["latest_invoice.payment_intent"],
        coupon: coupon.id,
      });
    } else {
      console.log(`Creating subscription without coupon`);
      // Create the subscription without a coupon
      subscription = await stripe.subscriptions.create({
        customer: customerId,
        items: [{ price: priceId }],
        payment_behavior: "default_incomplete",
        expand: ["latest_invoice.payment_intent"],
      });
    }

    console.log(`Subscription created: ${subscription.id}`);

    const invoice = subscription.latest_invoice as Stripe.Invoice;

    if (!invoice || typeof invoice === "string") {
      throw new Error("Invoice is missing or invalid");
    }

    const paymentIntent = invoice.payment_intent;

    if (!paymentIntent || typeof paymentIntent === "string") {
      console.log(`Creating new payment intent for subscription ${subscription.id}`);
      const newPaymentIntent = await stripe.paymentIntents.create({
        amount: invoice.amount_due,
        currency: invoice.currency,
        customer: customerId,
        automatic_payment_methods: { enabled: true },
      });

      console.log(`New payment intent created: ${newPaymentIntent.id}`);

      return NextResponse.json({
        code: "subscription_created_with_new_payment_intent",
        subscriptionId: subscription.id,
        clientSecret: newPaymentIntent.client_secret,
        isFirstSubscription,
        discountApplied: isFirstSubscription ? FIRST_TIME_DISCOUNT_PERCENT : 0,
      });
    }

    console.log(`Using existing payment intent: ${paymentIntent.id}`);

    return NextResponse.json({
      code: "subscription_created",
      subscriptionId: subscription.id,
      clientSecret: paymentIntent.client_secret,
      isFirstSubscription,
      discountApplied: isFirstSubscription ? FIRST_TIME_DISCOUNT_PERCENT : 0,
    });
  } catch (error: any) {
    console.error("Error in subscription creation:", error);
    return NextResponse.json(
      { code: "subscription_creation_failed", error: error.message },
      { status: 400 }
    );
  }
}
export async function DELETE(request: NextRequest) {
  try {
    const { subscriptionId } = await request.json();
    const deletedSubscription = await stripe.subscriptions.cancel(
      subscriptionId
    );

    return NextResponse.json({
      code: "subscription_deleted",
      deletedSubscription,
    });
  } catch (error) {
    console.error(error);
    return NextResponse.json(
      { code: "subscription_deletion_failed", error },
      { status: 400 }
    );
  }
}
