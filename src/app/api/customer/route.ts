import { NextRequest, NextResponse } from 'next/server';
import <PERSON><PERSON> from 'stripe';

const stripe = new Stripe(
  process.env.NODE_ENV === 'production'
    ? process.env.STRIPE_SECRET_KEY_LIVE!
    : process.env.STRIPE_SECRET_KEY_TEST!,
  {
    apiVersion: '2024-06-20',
  }
);

export async function POST(request: NextRequest) {
    try {
        const body = await request.json();
        console.log('Received body:', body);
        const email = body.email;

        if (!email) {
            console.error("Email is undefined or empty");
            return NextResponse.json(
                { code: 'invalid_email', error: 'Email is required' },
                { status: 400 }
            );
        }

        console.log(`Received email: ${email}`);

        const existingCustomers = await stripe.customers.list({ email: email, limit: 1 });
        //console.log(`Existing customers: ${JSON.stringify(existingCustomers.data)}`);

        let customer;
        if (existingCustomers.data.length > 0) {
            customer = existingCustomers.data[0];
            console.log("Existing customer found:", customer.id);
        } else {
            customer = await stripe.customers.create({ email });
            console.log("New customer created:", customer.id);
        }

        return NextResponse.json({
            code: 'customer_created_or_retrieved',
            customer,
        });
    } catch (error) {
        console.error("Error in customer creation/retrieval:", error);
        return NextResponse.json(
            { code: 'customer_creation_failed', error },
            { status: 400 }
        );
    }
}