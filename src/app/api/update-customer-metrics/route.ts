import { NextRequest, NextResponse } from 'next/server';
import <PERSON><PERSON> from 'stripe';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY as string, {
    apiVersion: '2024-06-20',
});

export async function POST(request: NextRequest) {
    try {
        const { customerId, metrics, gender } = await request.json();

        if (!customerId) {
            return NextResponse.json(
                { code: 'customer_update_failed', error: 'Customer ID is required' },
                { status: 400 }
            );
        }
        
        const sanitizedMetrics = {
            age: metrics.age,
            height: metrics.unit === 'imperial'
                ? `${metrics.heightFt}'${metrics.heightIn}"`
                : `${metrics.heightCm}cm`,
            weight: `${metrics.weight}${metrics.unit === 'imperial' ? 'lbs' : 'kg'}`,
            desired_weight: `${metrics.desiredWeight}${metrics.unit === 'imperial' ? 'lbs' : 'kg'}`,
            unit: metrics.unit
        };

        const updatedCustomer = await stripe.customers.update(customerId, {
            metadata: {
                metrics: JSON.stringify(sanitizedMetrics),
                gender: gender
            },
        });

        return NextResponse.json({
            code: 'customer_updated',
            customer: updatedCustomer,
        });
    } catch (error) {
        console.error("Error updating customer:", error);
        return NextResponse.json(
            { code: 'customer_update_failed', error },
            { status: 500 }
        );
    }
}