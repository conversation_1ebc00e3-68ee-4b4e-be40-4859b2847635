"use client";
import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import Image from "next/image";
import { quizData } from "@/data/quiz-data";
import CheckoutModal from "@/components/Checkout/CheckoutModal";
import { useDispatch, useSelector } from "react-redux";
import { setCustomerId } from "@/app/redux/customerSlice";
import { RootState } from "@/app/redux/store";
import useFacebookPixel from "@/hooks/useFacebookPixel";

const question = quizData["offer"];

function QuizOfferContent() {
  const router = useRouter();
  const [selectedPlan, setSelectedPlan] = useState("3month");
  const [openFaq, setOpenFaq] = useState(1);
  const [timer, setTimer] = useState<number>(600); // 10 minutes in seconds
  const [isCheckoutOpen, setIsCheckoutOpen] = useState(false);
  const promoCode = "face_may25";
  const dispatch = useDispatch();
  const userEmail = useSelector((state: RootState) => state.lymph.userEmail);
  const customerId = useSelector((state: RootState) => state.customer.id);
  const [includePdf, setIncludePdf] = useState(true);

  // Facebook Pixel tracking
  const { trackInitiateCheckout } = useFacebookPixel();

  useEffect(() => {
    if (timer > 0) {
      const interval = setInterval(() => setTimer((t: number) => t - 1), 1000);
      return () => clearInterval(interval);
    }
  }, [timer]);

  const formatTime = (t: number) => {
    const m = String(Math.floor(t / 60)).padStart(2, "0");
    const s = String(t % 60).padStart(2, "0");
    return { m, s };
  };
  const { m, s } = formatTime(timer);

  // Plan data with per day pricing
  const plans = [
    {
      id: "7day",
      label: "7-Day Plan",
      oldPrice: "$29.99",
      newPrice: "$13.99",
      perDay: `$${(13.99 / 7).toFixed(2)}`,
      days: 7,
      selected: selectedPlan === "7day",
      popular: false,
    },
    {
      id: "1month",
      label: "1-Month Plan",
      oldPrice: "$44.75",
      newPrice: "$19.99",
      perDay: `$${(19.99 / 30).toFixed(2)}`,
      days: 30,
      selected: selectedPlan === "1month",
      popular: true,
    },
    {
      id: "3month",
      label: "3-Month Plan",
      oldPrice: "$69.95",
      newPrice: "$29.99",
      perDay: `$${(29.99 / 90).toFixed(2)}`,
      days: 90,
      selected: selectedPlan === "3month",
      popular: false,
    },
  ];

  // Update disclaimers to use links for Terms of Use and support
  const planDisclaimers: Record<string, JSX.Element> = {
    "7day": (
      <>
        By clicking &quot;GET MY PLAN&quot;, you will be charged $13.99 after
        the payment confirmation. The subscription will auto-renew monthly after
        a 1-week intro offer at the full price of $44.79. Payments are charged
        in USD. To learn more, visit our{" "}
        <a
          href="https://www.znaturals.org/termsofservice"
          target="_blank"
          rel="noopener noreferrer"
          className="underline"
        >
          Terms of Use
        </a>{" "}
        or{" "}
        <a
          href="https://www.znaturals.org/privacy"
          target="_blank"
          rel="noopener noreferrer"
          className="underline"
        >
          support
        </a>
        .
      </>
    ),
    "1month": (
      <>
        By clicking &quot;GET MY PLAN&quot;, you will be charged $19.99 after
        the payment confirmation. The subscription will auto-renew monthly at
        the full price of $44.79. Payments are charged in USD. To learn more,
        visit our{" "}
        <a
          href="https://www.znaturals.org/termsofservice"
          target="_blank"
          rel="noopener noreferrer"
          className="underline"
        >
          Terms of Use
        </a>{" "}
        or{" "}
        <a
          href="https://www.znaturals.org/privacy"
          target="_blank"
          rel="noopener noreferrer"
          className="underline"
        >
          support
        </a>
        .
      </>
    ),
    "3month": (
      <>
        By clicking &quot;GET MY PLAN&quot;, you will be charged $29.99 after
        the payment confirmation. The subscription will auto-renew every 3
        months at the full price of $69.95. Payments are charged in USD. To
        learn more, visit our{" "}
        <a
          href="https://www.znaturals.org/termsofservice"
          target="_blank"
          rel="noopener noreferrer"
          className="underline"
        >
          Terms of Use
        </a>{" "}
        or{" "}
        <a
          href="https://www.znaturals.org/privacy"
          target="_blank"
          rel="noopener noreferrer"
          className="underline"
        >
          support
        </a>
        .
      </>
    ),
  };

  // Get selected plan details for tracking
  const getSelectedPlanDetails = () => {
    const plan = plans.find((p) => p.id === selectedPlan);
    return {
      value: Number(plan?.newPrice.replace("$", "") || "0"),
      contentIds: [`LymphDrainage-${selectedPlan}`],
      contentType: "subscription",
    };
  };

  // Update handleGetMyPlan to use Redux state and track InitiateCheckout
  const handleGetMyPlan = async () => {
    try {
      console.log('🛒 User clicked "Get My Plan" button');
      console.log("📧 Initiating checkout process for plan:", selectedPlan);

      if (!userEmail) {
        console.error("No email found in Redux state");
        alert("Please enter your email first");
        router.push("/quiz/plan");
        return;
      }

      // Track InitiateCheckout event asynchronously (fire-and-forget)
      const planDetails = getSelectedPlanDetails();

      // Fire the tracking in the background without blocking the UI
      trackInitiateCheckout(
        planDetails.contentIds,
        planDetails.contentType,
        userEmail,
        planDetails.value,
        "USD"
      )
        .then(() => {
          console.log(
            "✅ InitiateCheckout event tracked successfully for plan:",
            selectedPlan
          );
        })
        .catch((error) => {
          console.error("❌ Failed to track InitiateCheckout event:", error);
        });

      // Start modal opening immediately for better UX
      setIsCheckoutOpen(true);
      console.log("🚀 Checkout modal opened immediately");

      // Check if customer already exists in Redux state
      const existingCustomerId = customerId;

      if (existingCustomerId) {
        console.log(
          "✅ Using existing customer ID from Redux:",
          existingCustomerId
        );
        // Customer already exists, no need to create a new one
        return;
      }

      // Create customer only if not already exists (fallback for edge cases)
      const createCustomer = async () => {
        try {
          console.log(
            "🔄 Creating fallback Stripe customer for email:",
            userEmail
          );
          const customerResponse = await fetch("/api/customer", {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({ email: userEmail }),
          });

          if (!customerResponse.ok) {
            throw new Error("Failed to create/retrieve customer");
          }

          const { customer } = await customerResponse.json();
          console.log("✅ Fallback customer created/retrieved:", customer.id);

          // Store customer ID in Redux
          dispatch(setCustomerId(customer.id));
          console.log("💾 Customer ID stored in Redux");
        } catch (error) {
          console.error("❌ Error creating fallback customer:", error);
          // Don't close modal on customer creation failure
          // Modal can still function without pre-created customer
        }
      };

      // Create customer in background (non-blocking) only if needed
      createCustomer();
    } catch (error) {
      console.error("Error in handleGetMyPlan:", error);
      alert("An error occurred. Please try again.");
    }
  };

  return (
    <div className="flex flex-col min-h-screen bg-white">
      {/* Header */}
      <div className="w-full bg-gradient-to-br from-[#0F403D] via-[#0E6661] to-[#0F403D] rounded-b-3xl px-6 pt-8 pb-10 text-white relative overflow-hidden flex flex-col items-center">
        {/* Decorative icons */}
        <div className="absolute left-8 top-16 opacity-40 z-0">
          <svg width="32" height="32">
            <circle cx="16" cy="16" r="2" fill="#B6CFC7" />
            <line
              x1="16"
              y1="0"
              x2="16"
              y2="32"
              stroke="#B6CFC7"
              strokeWidth="1.5"
            />
            <line
              x1="0"
              y1="16"
              x2="32"
              y2="16"
              stroke="#B6CFC7"
              strokeWidth="1.5"
            />
          </svg>
        </div>
        <div className="absolute right-8 top-10 opacity-40 z-0">
          <svg width="32" height="32">
            <circle cx="16" cy="16" r="2" fill="#B6CFC7" />
            <line
              x1="16"
              y1="0"
              x2="16"
              y2="32"
              stroke="#B6CFC7"
              strokeWidth="1.5"
            />
            <line
              x1="0"
              y1="16"
              x2="32"
              y2="16"
              stroke="#B6CFC7"
              strokeWidth="1.5"
            />
          </svg>
        </div>
        <h2 className="text-3xl md:text-4xl font-bold text-white mb-3 leading-tight">
          {question.title}
        </h2>
        <p className="text-white/70 text-base md:text-lg mb-2">
          {question.subheading}
        </p>
      </div>
      {/* Focus Areas Card */}
      <div className="w-full flex justify-center mb-4 px-6 pt-6">
        <div className="bg-[#FFF5E5] border border-[#E0E0E0] rounded-3xl shadow-md p-6 flex flex-col items-center max-w-md w-full">
          <div className="flex flex-col items-center mb-4">
            <div className="w-14 h-14 rounded-full bg-[#B6CFC7] flex items-center justify-center mb-2 shadow">
              <Image
                src="/images/offer-focus-area.svg"
                alt="Focus Area"
                width={48}
                height={48}
                className="w-12 h-12"
                priority
              />
            </div>
            <div className="text-base font-bold text-[#184C43]">
              {question.focusAreas?.title}
            </div>
          </div>
          <div className="flex flex-wrap gap-2 justify-center mb-2">
            {question.focusAreas?.areas.map((area) => (
              <span key={area} className="text-xs text-[#184C43]/80">
                • {area}
              </span>
            ))}
          </div>
          <div className="flex flex-wrap gap-2 justify-center">
            {question.focusAreas?.tags.map((tag) => (
              <span
                key={tag}
                className="bg-white rounded-full px-3 py-1 text-xs border border-[#E0E0E0]"
              >
                {tag}
              </span>
            ))}
          </div>
        </div>
      </div>
      {/* Your Plan is here section (replace old plan selection UI) */}
      <div className="w-full flex justify-center mb-4 px-6">
        <div className="bg-[#F5F5F5] rounded-3xl p-6 flex flex-col items-center max-w-md w-full">
          {/* Before/After Image */}
          <div className="w-full h-32 rounded-xl mb-4 flex items-center justify-center">
            <Image
              src="/images/offer-before-after.svg"
              alt="Before and After Results"
              width={400}
              height={128}
              className="w-full h-full object-contain"
              priority
            />
          </div>
          <div className="font-bold text-lg text-[#184C43] mb-1 text-center">
            Get 2x Faster Results —<br />
            Visible Changes in the Very First Session
          </div>
          <div className="text-xs text-[#184C43]/80 mb-4 text-center">
            Join thousands who trust Lymph for sculpted, glowing skin;
            personalized to your face and lifestyle.
          </div>
          {/* Promo Code Bar (restyled, compact) */}
          <div className="w-full flex justify-center mb-4">
            <div className="bg-gradient-to-r from-[#c6e48b] to-[#eaf1ed] rounded-xl px-4 py-2 flex flex-row items-center gap-3 shadow border border-[#B6CFC7] w-full max-w-md min-w-0">
              <span className="flex items-center gap-2">
                <span className="bg-[#184C43] text-white rounded-full px-3 py-1 text-xs md:text-sm font-semibold flex items-center min-w-[60px] justify-center md:min-w-[80px] whitespace-nowrap">
                  Promo
                </span>
                <span className="bg-white border border-[#B6CFC7] rounded px-2 py-0.5 text-[#184C43] font-mono font-semibold text-xs md:text-sm flex items-center">
                  {promoCode}
                </span>
              </span>
              <span className="ml-auto text-[#184C43] font-semibold text-xs sm:text-sm whitespace-nowrap flex items-center gap-1 max-w-[100px] overflow-hidden">
                <span className="font-mono">{m}</span>
                <span className="font-normal">min</span>
                <span>:</span>
                <span className="font-mono">{s}</span>
                <span className="font-normal">sec</span>
              </span>
            </div>
          </div>
          {/* Plan Selection UI (green theme, no free PDF, 'Most Popular' badge) */}
          <div className="w-full flex flex-col items-center mb-6">
            {/* Plan Cards */}
            <div className="w-full flex flex-col gap-4">
              {plans.map((plan, idx) => (
                <div
                  key={plan.id}
                  className={`relative rounded-2xl border-2 transition-all duration-200 p-4 flex flex-row items-center justify-between cursor-pointer shadow-sm ${
                    plan.selected
                      ? "border-[#184C43] bg-[#EAF1ED]"
                      : "border-[#B6CFC7] bg-white"
                  }`}
                  onClick={() => setSelectedPlan(plan.id)}
                  style={{ minHeight: 72 }}
                >
                  {/* Most Popular Badge */}
                  {plan.popular && (
                    <div className="absolute -top-4 left-4 bg-[#184C43] text-white text-xs font-bold px-3 py-1 rounded-full shadow">
                      MOST POPULAR
                    </div>
                  )}
                  {/* Radio Button */}
                  <span
                    className={`w-6 h-6 flex items-center justify-center rounded-full border-2 ${
                      plan.selected
                        ? "border-[#184C43] bg-[#184C43]"
                        : "border-[#B6CFC7] bg-white"
                    } transition-all mr-4`}
                  >
                    {plan.selected && (
                      <svg
                        width="16"
                        height="16"
                        fill="none"
                        viewBox="0 0 16 16"
                      >
                        <circle cx="8" cy="8" r="7" fill="#184C43" />
                        <path
                          d="M5 8l2 2 4-4"
                          stroke="#fff"
                          strokeWidth="1.5"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        />
                      </svg>
                    )}
                  </span>
                  {/* Plan Info */}
                  <div className="flex flex-col flex-1">
                    <span className="font-bold text-base text-[#184C43] leading-tight">
                      {plan.label}
                    </span>
                    <div className="flex items-center gap-2 mt-1">
                      <span className="text-xs text-[#B6CFC7] line-through">
                        {plan.oldPrice}
                      </span>
                      <span className="font-bold text-[#184C43] text-base">
                        {plan.newPrice}
                      </span>
                    </div>
                  </div>
                  {/* Per Day Pricing */}
                  <div className="flex flex-col items-end">
                    <span className="font-bold text-lg text-[#184C43]">
                      {plan.perDay}
                    </span>
                    <span className="text-xs text-[#888]">per day</span>
                  </div>
                </div>
              ))}
            </div>
            {/* CTA Button */}
            <div className="w-full flex justify-center mt-6">
              <button
                onClick={handleGetMyPlan}
                className="w-full py-4 rounded-2xl font-bold text-lg bg-[#184C43] text-white shadow-lg hover:bg-[#0F403D] transition-all"
                style={{ letterSpacing: 1 }}
              >
                Get My Plan
              </button>
            </div>
          </div>
          {/* Fine Print */}
          <div className="w-full flex justify-center mb-2">
            <div className="w-full text-xs text-[#888] text-center">
              {planDisclaimers[selectedPlan]}
            </div>
          </div>
        </div>
      </div>
      {/* Money Back Guarantee Card */}
      <div className="w-full flex justify-center mb-2 px-6">
        <div className="bg-gradient-to-br from-[#0F403D] via-[#0E6661] to-[#0F403D] rounded-2xl p-6 flex flex-col items-center max-w-md w-full text-white text-center">
          <div className="mb-2">{question.guarantee?.title}</div>
          <div className="text-xs mb-2">{question.guarantee?.text}</div>
          <div className="flex gap-2 justify-center text-xs opacity-80">
            {question.guarantee?.links.map((link, idx) => (
              <React.Fragment key={link}>
                <span>{link}</span>
                {idx < question.guarantee!.links.length - 1 && <span>|</span>}
              </React.Fragment>
            ))}
          </div>
        </div>
      </div>
      {/* Backed by Women's Health */}
      {/* <div className="w-full flex justify-center mb-6 px-6">
        <div className="flex flex-col items-center max-w-md w-full">
          <div className="text-xs text-[#888] mb-1">Backed by</div>
          <div className="font-bold text-lg mb-1">{question.backedBy?.title}</div>
          <div className="text-xs text-[#888] text-center">{question.backedBy?.text}</div>
        </div>
      </div> */}
      {/* What You Get Card */}
      <div className="w-full flex justify-center mb-4 px-6">
        <div className="bg-[#F5F5F5] rounded-3xl p-6 flex flex-col items-center max-w-md w-full">
          <div className="font-bold text-lg text-[#184C43] mb-2">
            What You Get
          </div>
          <div className="text-xs text-[#184C43]/80 mb-4 text-center">
            Science-backed care, made simple — and made for you.
          </div>
          <div className="w-full flex flex-col gap-3">
            {question.features?.map((item) => (
              <div
                key={item.title}
                className="flex items-center gap-3 bg-white rounded-xl p-3"
              >
                <div className="w-10 h-10 rounded-full flex items-center justify-center">
                  {item.title === "Lymphatic Self Massage Videos" && (
                    <Image
                      src="/images/offer-lymphatic-self-massage-videos.svg"
                      alt="Lymphatic Massage"
                      width={40}
                      height={40}
                      className="w-8 h-8"
                      priority
                    />
                  )}
                  {item.title === "Daily Detox Tips & Tracker" && (
                    <Image
                      src="/images/offer-daily-detox.svg"
                      alt="Daily Detox"
                      width={40}
                      height={40}
                      className="w-8 h-8"
                      priority
                    />
                  )}
                  {item.title === "Face Yoga" && (
                    <Image
                      src="/images/offer-face-yoga.svg"
                      alt="Face Yoga"
                      width={40}
                      height={40}
                      className="w-8 h-8"
                      priority
                    />
                  )}
                  {item.title === "Stress Relief Guides" && (
                    <Image
                      src="/images/offer-stress-relief-guides.svg"
                      alt="Stress Relief"
                      width={40}
                      height={40}
                      className="w-8 h-8"
                      priority
                    />
                  )}
                </div>
                <div>
                  <div className="font-semibold text-[#184C43] text-sm">
                    {item.title}
                  </div>
                  <div className="text-xs text-[#184C43]/70">{item.desc}</div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
      {/* Glow Begins Card */}
      <div className="w-full flex justify-center mb-4 px-6">
        <div className="bg-gradient-to-br from-[#FFE5D1] to-[#E5F5E5] rounded-3xl p-6 flex flex-col items-center max-w-md w-full">
          <div className="font-bold text-lg text-[#184C43] mb-2">
            Glow begins with a ritual—
            <br />
            make it yours <span>✨</span>
          </div>
          <div className="text-xs text-[#184C43]/80 mb-4 text-center">
            Claim your personalized lymph ritual—gently accelerate your glow, 2x
            faster in just a week, today!
          </div>
          {/* Ritual Glow Image */}
          <div className="w-full h-48 rounded-xl mb-2 flex items-center justify-center">
            <Image
              src="/images/offer-glow-ritual.svg"
              alt="Ritual Glow"
              width={400}
              height={192}
              className="w-full h-full object-contain"
              priority
            />
          </div>
        </div>
      </div>
      {/* Second part: Before/After, Reviews, FAQ, Final CTA */}
      {/* Before/After 2 */}
      <div className="w-full flex justify-center mb-4 px-6">
        <div className="bg-[#F5F5F5] rounded-3xl p-6 flex flex-col items-center max-w-md w-full">
          <div className="font-bold text-lg text-[#184C43] mb-2 text-center">
            Thousands Trust Lymph —<br />
            And Their Skin Shows It.
          </div>
          <div className="text-xs text-[#184C43]/80 mb-2 text-center">
            More Than a Trend — It&apos;s Transformation.
          </div>
          {/* Before/After Image */}
          <div className="w-full h-48 rounded-xl mb-2 flex items-center justify-center">
            <Image
              src="/images/offer-before-after-real.svg"
              alt="Real Before and After Results"
              width={400}
              height={192}
              className="w-full h-full object-contain"
              priority
            />
          </div>
        </div>
      </div>
      {/* Reviews */}
      <div className="w-full flex justify-center mb-4 px-6">
        <div className="bg-[#F5F5F5] rounded-3xl p-6 flex flex-col items-center max-w-md w-full">
          <div className="font-bold text-lg text-[#184C43] mb-2 text-center">
            Loved by 5,000+ Women & Wellness Experts
          </div>
          {question.reviews?.map((review, idx) => (
            <div
              key={idx}
              className="w-full bg-white rounded-xl p-3 mb-2 flex flex-col"
            >
              <div className="flex items-center gap-2 mb-1">
                <span className="w-6 h-6 rounded-full bg-[#E0E0E0] flex items-center justify-center overflow-hidden">
                  {review.name === "Mandy" && (
                    <Image
                      src="/images/offer-mandy.svg"
                      alt="Mandy"
                      width={24}
                      height={24}
                      className="w-full h-full object-cover"
                      priority
                    />
                  )}
                  {review.name === "Alicia" && (
                    <Image
                      src="/images/offer-alicia.svg"
                      alt="Alicia"
                      width={24}
                      height={24}
                      className="w-full h-full object-cover"
                      priority
                    />
                  )}
                  {review.name === "Rebecca" && (
                    <Image
                      src="/images/offer-rebecca.svg"
                      alt="Rebecca"
                      width={24}
                      height={24}
                      className="w-full h-full object-cover"
                      priority
                    />
                  )}
                  {review.name === "Emmet" && (
                    <Image
                      src="/images/offer-emmet.svg"
                      alt="Emmet"
                      width={24}
                      height={24}
                      className="w-full h-full object-cover"
                      priority
                    />
                  )}
                </span>
                <span className="font-semibold text-[#184C43] text-sm">
                  {review.name}
                </span>
                <span className="flex gap-1 ml-auto text-[#FFD700]">
                  {"★".repeat(review.rating)}
                </span>
              </div>
              <div className="text-xs text-[#184C43]/80">{review.text}</div>
              <div className="text-[10px] text-[#888] mt-1">
                • {review.location}
              </div>
            </div>
          ))}
        </div>
      </div>
      {/* FAQ Accordion */}
      <div className="w-full flex justify-center mb-4 px-6">
        <div className="bg-[#F5F5F5] rounded-3xl p-6 flex flex-col items-center max-w-md w-full">
          <div className="font-bold text-lg text-[#184C43] mb-2 text-center">
            Got Questions?
            <br />
            We&apos;ve Got You.
          </div>
          {question.faqs?.map((faq, idx) => (
            <div key={faq.q} className="w-full mb-2">
              <button
                className="w-full flex justify-between items-center py-3 px-4 bg-white rounded-xl text-left text-sm font-medium text-[#184C43] focus:outline-none transition-colors duration-200"
                onClick={() => setOpenFaq(openFaq === idx ? -1 : idx)}
              >
                {faq.q}
                <span
                  className={`ml-2 transition-transform duration-300 ${
                    openFaq === idx ? "rotate-180" : ""
                  }`}
                >
                  {openFaq === idx ? "-" : "+"}
                </span>
              </button>
              <div
                className={`overflow-hidden transition-all duration-300 ${
                  openFaq === idx
                    ? "max-h-40 opacity-100 mt-2"
                    : "max-h-0 opacity-0 mt-0"
                }`}
                style={{
                  pointerEvents: openFaq === idx ? "auto" : "none",
                }}
              >
                <div className="bg-[#E0E0E0] rounded-b-xl px-4 py-3 text-xs text-[#184C43]/80">
                  {faq.a}
                </div>
              </div>
            </div>
          ))}
          <div className="text-xs text-[#888] mt-4 text-center">
            Still having issues? Reach out to us anytime at{" "}
            <a href="mailto:<EMAIL>" className="underline">
              <EMAIL>
            </a>
          </div>
        </div>
      </div>
      {/* Final CTA Card */}
      <div className="w-full flex justify-center mb-8 px-6">
        <div className="bg-gradient-to-br from-[#0F403D] via-[#0E6661] to-[#0F403D] rounded-3xl p-6 flex flex-col items-center max-w-md w-full text-white text-center">
          {/* Perfect Skin Image */}
          <div className="w-48 h-48 mb-4 flex items-center justify-center">
            <Image
              src="/images/offer-perfect-skin.svg"
              alt="Perfect Skin"
              width={192}
              height={192}
              className="w-full h-full object-contain"
              priority
            />
          </div>
          <div className="font-bold text-lg mb-2">
            Don&apos;t Wait for perfect skin — It&apos;s already waiting for
            you.
          </div>
          <div className="text-xs mb-4">
            Claim your plan now before spots get all used up
          </div>
          <button
            onClick={handleGetMyPlan}
            className="bg-[#c6e48b] text-[#184C43] rounded-full px-6 py-3 font-semibold text-lg hover:bg-[#b6d97a] transition-all"
          >
            Claim Now!
          </button>
        </div>
      </div>

      {/* Add the CheckoutModal */}
      <CheckoutModal
        isOpen={isCheckoutOpen}
        onClose={() => setIsCheckoutOpen(false)}
        selectedPlan={selectedPlan}
        total={Number(
          (plans.find((p) => p.id === selectedPlan)?.newPrice || "0").replace(
            "$",
            ""
          )
        )}
      />
    </div>
  );
}

export default function QuizOfferPage() {
  return <QuizOfferContent />;
}
