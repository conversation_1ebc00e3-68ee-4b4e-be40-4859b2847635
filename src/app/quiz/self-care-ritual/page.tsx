"use client";
import React, { useState, useEffect, useCallback } from "react";
import { ArrowRight } from "lucide-react";
import { useRouter } from "next/navigation";
import { LymphQuizProvider, useLymphQuiz } from "../LymphQuizContext";
import { quizData } from "@/data/quiz-data";

const question = quizData["self-care-ritual"];

function SelfCareRitualContent() {
  const { state, setAnswer } = useLymphQuiz();
  
  // Initialize from Redux state if available
  const savedAnswers = state.answers[question.id] || [];
  const parsedAnswers = savedAnswers.length ? JSON.parse(savedAnswers[0]) : null;
  
  const [selectedDays, setSelectedDays] = useState<string[]>(
    parsedAnswers?.days || []
  );
  const [selectedTime, setSelectedTime] = useState(
    parsedAnswers?.time || question.timesOfDay?.[0]?.value || "morning"
  );
  const [selectedDuration, setSelectedDuration] = useState(
    parsedAnswers?.duration || question.durations?.[0] || 10
  );
  
  const router = useRouter();

  // Use a ref to track if we need to update Redux
  const [shouldUpdateRedux, setShouldUpdateRedux] = useState(false);

  // Save to Redux when selections change, but avoid infinite loop
  useEffect(() => {
    // Only save if user has made at least one selection and we should update
    if (selectedDays.length > 0 && shouldUpdateRedux) {
      const answerData = JSON.stringify({
        days: selectedDays,
        time: selectedTime,
        duration: selectedDuration
      });
      
      setAnswer(question.id, [answerData]);
      setShouldUpdateRedux(false); // Reset flag after update
    }
  }, [selectedDays, selectedTime, selectedDuration, question.id, setAnswer, shouldUpdateRedux]);

  const handleDayClick = (day: string) => {
    setSelectedDays(prev =>
      prev.includes(day)
        ? prev.filter(d => d !== day)
        : [...prev, day]
    );
    setShouldUpdateRedux(true); // Set flag to update Redux
  };

  const handleTimeChange = (time: string) => {
    setSelectedTime(time);
    setShouldUpdateRedux(true); // Set flag to update Redux
  };

  const handleDurationChange = (duration: number) => {
    setSelectedDuration(duration);
    setShouldUpdateRedux(true); // Set flag to update Redux
  };

  const handleDone = () => {
    // Ensure data is saved before navigating
    const answerData = JSON.stringify({
      days: selectedDays,
      time: selectedTime,
      duration: selectedDuration
    });
    
    setAnswer(question.id, [answerData]);
    
    // If user selected morning, go to rhythm page, otherwise skip to science-backed
    const nextPage = selectedTime === "morning" ? "/quiz/rhythm" : "/quiz/science-backed";
    router.push(nextPage);
  };

  return (
    <div className="flex flex-col min-h-screen bg-white">
      {/* Green header with progress bar and title */}
      <div className="w-full bg-gradient-to-br from-[#0F403D] via-[#0E6661] to-[#0F403D] rounded-b-3xl px-4 pt-8 pb-10 md:pb-14 text-white relative overflow-hidden flex flex-col items-center">
        {/* Decorative icons */}
        <div className="absolute left-8 top-16 opacity-40 z-0">
          <svg width="32" height="32"><circle cx="16" cy="16" r="2" fill="#B6CFC7"/><line x1="16" y1="0" x2="16" y2="32" stroke="#B6CFC7" strokeWidth="1.5"/><line x1="0" y1="16" x2="32" y2="16" stroke="#B6CFC7" strokeWidth="1.5"/></svg>
        </div>
        <div className="absolute right-8 top-10 opacity-40 z-0">
          <svg width="32" height="32"><circle cx="16" cy="16" r="2" fill="#B6CFC7"/><line x1="16" y1="0" x2="16" y2="32" stroke="#B6CFC7" strokeWidth="1.5"/><line x1="0" y1="16" x2="32" y2="16" stroke="#B6CFC7" strokeWidth="1.5"/></svg>
        </div>
        {/* Progress bar */}
        <div className="w-full max-w-lg mx-auto mb-6">
          <div className="flex justify-between mb-2">
            <span className="text-sm text-white/70"> </span>
            <span className="text-sm text-white/80 font-medium">{question.progress}%</span>
          </div>
          <div className="h-2 bg-white/20 rounded-full overflow-hidden">
            <div className="h-full bg-[#c6e48b]" style={{ width: `${question.progress}%` }}></div>
          </div>
        </div>
        {/* Title and subtitle */}
        <div className="w-full max-w-lg mx-auto text-center z-10">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-3 leading-tight">
            {question.title}
          </h2>
          <p className="text-white/70 text-base md:text-lg mb-2">
            {question.subheading}
          </p>
        </div>
      </div>
      {/* Main card */}
      <div className="flex-1 w-full flex flex-col items-center bg-white px-4 pt-8 pb-16">
        <div className="w-full max-w-xl mx-auto flex flex-col gap-6">
          {/* Set Day */}
          <div className="bg-[#F5F5F5] rounded-2xl p-6 mb-2">
            <div className="text-xs text-[#184C43]/60 font-semibold mb-2">Set Day</div>
            <div className="text-[#184C43] font-medium mb-2">SELECT DAYS</div>
            <div className="flex overflow-x-auto gap-2 pb-2 hide-scrollbar">
              {question.weekdays?.map((day) => (
                <button
                  key={day}
                  className={`min-w-[64px] px-4 py-3 rounded-xl text-lg font-semibold border transition-all flex-shrink-0
                    ${selectedDays.includes(day) ? 'bg-[#184C43] text-white border-[#184C43]' : 'bg-white text-[#184C43] border-[#E0E0E0]'}
                  `}
                  onClick={() => handleDayClick(day)}
                >
                  {day}
                </button>
              ))}
            </div>
          </div>
          {/* Daily Timing */}
          <div className="bg-[#F5F5F5] rounded-2xl p-6 mb-2">
            <div className="text-xs text-[#184C43]/60 font-semibold mb-2">Daily Timing</div>
            <div className="text-[#184C43] font-medium mb-2">Choose When You&apos;d Like to Begin —</div>
            {/* Custom Dropdown Start */}
            <CustomDropdown
              options={question.timesOfDay ?? []}
              value={selectedTime}
              onChange={handleTimeChange}
            />
            {/* Custom Dropdown End */}
          </div>
          {/* Set Duration */}
          <div className="bg-[#F5F5F5] rounded-2xl p-6 mb-2">
            <div className="text-xs text-[#184C43]/60 font-semibold mb-2">Set Duration</div>
            <div className="text-[#184C43] font-medium mb-2">Select the amount of time you want to spend during your self-care ritual</div>
            <div className="grid grid-cols-3 sm:grid-cols-5 gap-2">
              {question.durations?.map((min) => (
                <button
                  key={min}
                  className={`py-2 rounded-xl text-base font-semibold border transition-all
                    ${selectedDuration === min ? 'bg-[#184C43] text-white border-[#184C43]' : 'bg-white text-[#184C43] border-[#E0E0E0]'}
                  `}
                  onClick={() => handleDurationChange(min)}
                >
                  {min.toString().padStart(2, '0')}:00
                </button>
              ))}
            </div>
          </div>
        </div>
        <button
          className="mt-12 w-full max-w-lg py-4 px-6 rounded-full font-medium transition-all flex items-center justify-center text-lg bg-[#c6e48b] text-[#184C43] hover:bg-[#b6d97a]"
          onClick={handleDone}
        >
          Done! <ArrowRight className="ml-2 h-5 w-5" />
        </button>
      </div>
    </div>
  );
}

function CustomDropdown({ options, value, onChange }: { options: { value: string, label: string }[], value: string, onChange: (v: string) => void }) {
  const [open, setOpen] = React.useState(false);
  const buttonRef = React.useRef<HTMLButtonElement>(null);
  const menuRef = React.useRef<HTMLDivElement>(null);

  // Close dropdown on outside click
  React.useEffect(() => {
    function handleClick(e: MouseEvent) {
      if (
        !buttonRef.current?.contains(e.target as Node) &&
        !menuRef.current?.contains(e.target as Node)
      ) {
        setOpen(false);
      }
    }
    if (open) document.addEventListener('mousedown', handleClick);
    return () => document.removeEventListener('mousedown', handleClick);
  }, [open]);

  // Keyboard navigation
  React.useEffect(() => {
    if (!open) return;
    const handleKey = (e: KeyboardEvent) => {
      if (e.key === 'Escape') setOpen(false);
    };
    window.addEventListener('keydown', handleKey);
    return () => window.removeEventListener('keydown', handleKey);
  }, [open]);

  const selected = options.find(opt => opt.value === value);

  return (
    <div className="relative">
      <button
        ref={buttonRef}
        type="button"
        className="w-full px-4 py-3 rounded-xl border text-lg font-medium bg-[#184C43] text-white flex items-center justify-between focus:outline-none"
        onClick={() => setOpen(o => !o)}
        aria-haspopup="listbox"
        aria-expanded={open}
      >
        <span>{selected?.label}</span>
        <svg width="20" height="20" fill="none" viewBox="0 0 20 20" className="ml-2">
          <path d="M6 8l4 4 4-4" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
        </svg>
      </button>
      {open && (
        <div
          ref={menuRef}
          className="absolute left-0 mt-2 w-full bg-white border border-[#E0E0E0] rounded-xl shadow-lg z-20"
          tabIndex={-1}
          role="listbox"
        >
          {options.map(opt => (
            <button
              key={opt.value}
              className={`w-full text-left px-4 py-3 text-lg rounded-xl transition-all
                ${value === opt.value ? 'bg-[#184C43] text-white' : 'text-[#184C43] hover:bg-[#eaf1ed]'}
              `}
              onClick={() => { onChange(opt.value); setOpen(false); }}
              role="option"
              aria-selected={value === opt.value}
            >
              {opt.label}
            </button>
          ))}
        </div>
      )}
    </div>
  );
}

export default function SelfCareRitualPage() {
  return (
    <LymphQuizProvider>
      <SelfCareRitualContent />
    </LymphQuizProvider>
  );
} 
