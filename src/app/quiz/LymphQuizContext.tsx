'use client';
import React, { useState, useContext, createContext, useEffect } from 'react';
import { useLymphQuizActions, useLymphQuizAnswers } from '../redux/lymphHooks';

// Keep the original interface for backward compatibility
interface LymphQuizState {
  answers: Record<string, string[]>;
}

interface LymphQuizContextType {
  state: LymphQuizState;
  setAnswer: (questionId: string, answer: string[]) => void;
}

const LymphQuizContext = createContext<LymphQuizContextType | undefined>(undefined);

export function useLymphQuiz() {
  const ctx = useContext(LymphQuizContext);
  if (!ctx) throw new Error('useLymphQuiz must be used within LymphQuizProvider');
  return ctx;
}

export function LymphQuizProvider({ children }: { children: React.ReactNode }) {
  // Get Redux actions and state
  const { saveQuizAnswer, saveAllQuizAnswers } = useLymphQuizActions();
  const reduxQuizAnswers = useLymphQuizAnswers();
  
  // Keep local state for immediate UI updates and backward compatibility
  const [state, setState] = useState<LymphQuizState>({ answers: {} });

  // Initialize state from localStorage or Redux - use useEffect to avoid render-time state updates
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const saved = localStorage.getItem('lymphQuizState');
      if (saved) {
        try {
          const parsedState = JSON.parse(saved);
          // Update local state
          setState(parsedState);
          
          // Sync with Redux on initial load - but don't do this during render
          if (parsedState.answers && Object.keys(parsedState.answers).length > 0) {
            console.log('Syncing localStorage answers with Redux:', parsedState.answers);
            saveAllQuizAnswers(parsedState.answers);
          }
        } catch (e) {
          console.error('Error parsing saved quiz state:', e);
        }
      } else if (Object.keys(reduxQuizAnswers).length > 0) {
        // If no localStorage but Redux has data, use Redux data
        setState({ answers: reduxQuizAnswers });
      }
    }
  }, [saveAllQuizAnswers]); // Add saveAllQuizAnswers to the dependency array

  // Sync local state changes to localStorage
  useEffect(() => {
    if (typeof window !== 'undefined') {
      localStorage.setItem('lymphQuizState', JSON.stringify(state));
    }
  }, [state]);

  // Sync Redux state to local state if Redux changes
  useEffect(() => {
    if (Object.keys(reduxQuizAnswers).length > 0) {
      setState(prev => ({
        ...prev,
        answers: reduxQuizAnswers
      }));
    }
  }, [reduxQuizAnswers]);

  const setAnswer = (questionId: string, answer: string[]) => {
    console.log('Setting answer in LymphQuizContext:', questionId, answer);
    
    // Update local state for immediate UI
    setState(prev => ({
      ...prev,
      answers: { ...prev.answers, [questionId]: answer },
    }));
    
    // Update Redux state
    saveQuizAnswer(questionId, answer);
  };

  return (
    <LymphQuizContext.Provider value={{ state, setAnswer }}>
      {children}
    </LymphQuizContext.Provider>
  );
} 
