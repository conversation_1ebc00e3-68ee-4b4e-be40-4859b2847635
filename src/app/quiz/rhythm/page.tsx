"use client";
import React from "react";
import { ArrowRight } from "lucide-react";
import { useRouter } from "next/navigation";
import { LymphQuizProvider, useLymphQuiz } from "../LymphQuizContext";
import { quizData } from "@/data/quiz-data";
import Image from "next/image";

const question = quizData["rhythm"];

function RhythmContent() {
  const router = useRouter();

  const handleNext = () => {
    router.push(question.nextPage);
  };

  return (
    <div className="flex flex-col min-h-screen bg-white">
      {/* Green header with progress bar and title */}
      <div className="w-full bg-gradient-to-br from-[#0F403D] via-[#0E6661] to-[#0F403D] rounded-b-3xl px-4 pt-8 pb-10 md:pb-14 text-white relative overflow-hidden flex flex-col items-center">
        {/* Decorative icons */}
        <div className="absolute left-8 top-16 opacity-40 z-0">
          <svg width="32" height="32"><circle cx="16" cy="16" r="2" fill="#B6CFC7"/><line x1="16" y1="0" x2="16" y2="32" stroke="#B6CFC7" strokeWidth="1.5"/><line x1="0" y1="16" x2="32" y2="16" stroke="#B6CFC7" strokeWidth="1.5"/></svg>
        </div>
        <div className="absolute right-8 top-10 opacity-40 z-0">
          <svg width="32" height="32"><circle cx="16" cy="16" r="2" fill="#B6CFC7"/><line x1="16" y1="0" x2="16" y2="32" stroke="#B6CFC7" strokeWidth="1.5"/><line x1="0" y1="16" x2="32" y2="16" stroke="#B6CFC7" strokeWidth="1.5"/></svg>
        </div>
        {/* Progress bar */}
        <div className="w-full max-w-lg mx-auto mb-6">
          <div className="flex justify-between mb-2">
            <span className="text-sm text-white/70"> </span>
            <span className="text-sm text-white/80 font-medium">{question.progress}%</span>
          </div>
          <div className="h-2 bg-white/20 rounded-full overflow-hidden">
            <div className="h-full bg-[#c6e48b]" style={{ width: `${question.progress}%` }}></div>
          </div>
        </div>
        {/* Title and subtitle */}
        <div className="w-full max-w-lg mx-auto text-center z-10">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-3 leading-tight">
            {question.title}
          </h2>
          <p className="text-white/70 text-base md:text-lg mb-2">
            {question.subheading}
          </p>
        </div>
      </div>
      {/* Card with pie chart and text */}
      <div className="flex-1 w-full flex flex-col items-center bg-white px-4 pt-8 pb-16">
        <div className="w-full max-w-md mx-auto bg-[#F5F5F5] rounded-3xl flex flex-col items-center justify-center p-8 shadow transition-all duration-500">
          <div className="text-center text-[#184C43] font-bold text-xl mb-2">{question.cardTitle}</div>
          <div className="text-center text-[#6B7A7A] text-sm mb-4">
            {question.cardSub}
          </div>
          {/* Pie chart */}
          <div className="flex items-center justify-center w-full mb-4">
            <Image
              src="/images/rhythm-pie-chart.svg"
              alt="Rhythm Pie Chart"
              width={220}
              height={220}
              className="object-contain"
              priority
            />
          </div>
          <div className="text-center text-[#6B7A7A] text-sm mt-2">
            {question.cardFooter}
          </div>
        </div>
        <button
          className="mt-8 w-full max-w-lg py-4 px-6 rounded-full font-medium transition-all flex items-center justify-center text-lg bg-[#c6e48b] text-[#184C43] hover:bg-[#b6d97a]"
          onClick={handleNext}
        >
          Next <ArrowRight className="ml-2 h-5 w-5" />
        </button>
      </div>
    </div>
  );
}

export default function RhythmPage() {
  return (
    <LymphQuizProvider>
      <RhythmContent />
    </LymphQuizProvider>
  );
} 