import { quizData } from "@/data/quiz-data";
import QuizPage from "@/components/QuizPage";

// This tells Next.js to pre-render this page at build time
export const dynamic = 'force-static';

// This ensures the page is statically generated
export const revalidate = false;

export default function LymphaticDrainageTechniquesPage() {
  return <QuizPage question={quizData["lymphatic-drainage-techniques"]} />;
} 