"use client";
import React from "react";
import { ArrowRight } from "lucide-react";
import { useRouter } from "next/navigation";
import Image from "next/image";
import { LymphQuizProvider, useLymphQuiz } from "../LymphQuizContext";
import { quizData } from "@/data/quiz-data";

const question = quizData["daily-water-intake"];

function Glass({ filled }: { filled: boolean }) {
  return (
    <svg width="36" height="60" viewBox="0 0 36 60" fill="none" className="mx-1" style={{ minWidth: 36 }}>
      <rect x="6" y="6" width="24" height="48" rx="6" stroke="#184C43" strokeWidth="2" fill="#fff" />
      {filled && <rect x="8" y="30" width="20" height="22" rx="4" fill="#4FC3F7" />} {/* blue water fill */}
    </svg>
  );
}

function DailyWaterIntakeContent() {
  const { state, setAnswer } = useLymphQuiz();
  // Get the selected value as a float (e.g., 2.5)
  const selected = parseFloat(state.answers[question.id]?.[0] || '0');
  const router = useRouter();

  // Helper to determine glass state
  const getGlassState = (idx: number) => {
    if (selected >= idx + 1) return 'full';
    if (selected === idx + 0.5) return 'half';
    return 'empty';
  };

  // Handle click logic
  const handleGlassClick = (idx: number) => {
    let newValue = 0;
    if (selected > idx) {
      // If clicking before current, set to idx+1 (full)
      newValue = idx + 1;
    } else if (selected === idx + 0.5) {
      // If already half, go to full
      newValue = idx + 1;
    } else if (selected === idx + 1) {
      // If already full, reset to empty
      newValue = idx;
    } else {
      // Otherwise, set to half
      newValue = idx + 0.5;
    }
    setAnswer(question.id, [String(newValue)]);
  };

  const handleNext = () => {
    if (selected > 0) {
      router.push(question.nextPage);
    }
  };

  return (
    <div className="flex flex-col min-h-screen bg-white">
      {/* Green header with progress bar, question, subheading */}
      <div className="w-full bg-gradient-to-br from-[#0F403D] via-[#0E6661] to-[#0F403D] rounded-b-3xl px-4 pt-8 pb-10 md:pb-14 text-white relative overflow-hidden flex flex-col items-center">
        {/* Decorative icons */}
        <div className="absolute left-8 top-16 opacity-40 z-0">
          <svg width="32" height="32"><circle cx="16" cy="16" r="2" fill="#B6CFC7"/><line x1="16" y1="0" x2="16" y2="32" stroke="#B6CFC7" strokeWidth="1.5"/><line x1="0" y1="16" x2="32" y2="16" stroke="#B6CFC7" strokeWidth="1.5"/></svg>
        </div>
        <div className="absolute right-8 top-10 opacity-40 z-0">
          <svg width="32" height="32"><circle cx="16" cy="16" r="2" fill="#B6CFC7"/><line x1="16" y1="0" x2="16" y2="32" stroke="#B6CFC7" strokeWidth="1.5"/><line x1="0" y1="16" x2="32" y2="16" stroke="#B6CFC7" strokeWidth="1.5"/></svg>
        </div>
        {/* Progress bar */}
        <div className="w-full max-w-lg mx-auto mb-6">
          <div className="flex justify-between mb-2">
            <span className="text-sm text-white/70"> </span>
            <span className="text-sm text-white/80 font-medium">{question.progress}%</span>
          </div>
          <div className="h-2 bg-white/20 rounded-full overflow-hidden">
            <div className="h-full bg-[#c6e48b]" style={{ width: `${question.progress}%` }}></div>
          </div>
        </div>
        {/* Question and subheading */}
        <div className="w-full max-w-lg mx-auto text-center z-10">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-3 leading-tight">
            {question.title}
          </h2>
          <p className="text-white/70 text-base md:text-lg mb-2">
            {question.subheading}
          </p>
        </div>
      </div>
      {/* Glasses selection and Next button */}
      <div className="flex-1 w-full flex flex-col items-center bg-white px-4 pt-8 pb-16">
        <div className="grid grid-cols-4 gap-4 mb-8" style={{maxWidth: 400}}>
          {[...Array(8)].map((_, i) => {
            const state = getGlassState(i);
            let src = "/images/daily-water-intake-empty.svg";
            if (state === 'half') src = "/images/daily-water-intake-half.svg";
            if (state === 'full') src = "/images/daily-water-intake-full.svg";
            return (
              <button
                key={i}
                type="button"
                aria-label={`Select glass ${i+1}`}
                className="bg-transparent border-none p-0 focus:outline-none"
                onClick={() => handleGlassClick(i)}
              >
                <Image
                  src={src}
                  alt={`${state} glass`}
                  width={48}
                  height={80}
                  style={{ transition: 'transform 0.2s' }}
                  priority
                />
              </button>
            );
          })}
        </div>
        <button
          className={`mt-16 w-full max-w-lg py-4 px-6 rounded-full font-medium transition-all flex items-center justify-center text-lg
            ${selected > 0
              ? 'bg-[#c6e48b] text-[#184C43] hover:bg-[#b6d97a]'
              : 'bg-[#c6e48b]/50 text-[#184C43]/50 cursor-not-allowed'}`}
          disabled={selected <= 0}
          onClick={handleNext}
        >
          Next <ArrowRight className="ml-2 h-5 w-5" />
        </button>
      </div>
    </div>
  );
}

export default function DailyWaterIntakePage() {
  return (
    <LymphQuizProvider>
      <DailyWaterIntakeContent />
    </LymphQuizProvider>
  );
} 