"use client";
import React, { useEffect, useState } from "react";
import { quizData } from "@/data/quiz-data";

const features = [
  {
    icon: (
      <svg width="24" height="24" fill="none" viewBox="0 0 24 24"><rect x="4" y="6" width="16" height="14" rx="3" stroke="#184C43" strokeWidth="1.5"/><path d="M8 10h8M8 14h5" stroke="#184C43" strokeWidth="1.5" strokeLinecap="round"/></svg>
    ),
    title: "Personalized Plan",
    desc: "Custom routines tailored to your specific skin needs and goals",
  },
  {
    icon: (
      <svg width="24" height="24" fill="none" viewBox="0 0 24 24"><rect x="4" y="4" width="16" height="16" rx="4" stroke="#184C43" strokeWidth="1.5"/><path d="M8 12h8M12 8v8" stroke="#184C43" strokeWidth="1.5" strokeLinecap="round"/></svg>
    ),
    title: "Expert Guidance",
    desc: "Video tutorials and step-by-step instructions for each technique",
  },
  {
    icon: (
      <svg width="24" height="24" fill="none" viewBox="0 0 24 24"><rect x="4" y="4" width="16" height="16" rx="4" stroke="#184C43" strokeWidth="1.5"/><path d="M8 16l4-4 4 4" stroke="#184C43" strokeWidth="1.5" strokeLinecap="round"/></svg>
    ),
    title: "Progress Tracking",
    desc: "Regular assessments and adjustments to optimize results",
  },
];

export default function RightHandsPage() {
  const [cardVisible, setCardVisible] = useState(false);
  const [visibleFeatures, setVisibleFeatures] = useState(0);
  const question = quizData["right-hands"];

  useEffect(() => {
    const cardTimeout = setTimeout(() => setCardVisible(true), 400);
    const featureInterval = setInterval(() => {
      setVisibleFeatures((prev) => {
        if (prev < features.length) return prev + 1;
        clearInterval(featureInterval);
        return prev;
      });
    }, 400);
    return () => {
      clearTimeout(cardTimeout);
      clearInterval(featureInterval);
    };
  }, []);

  const handleCta = () => {
    window.location.href = question.nextPage;
  };

  return (
    <div className="flex flex-col min-h-screen bg-white">
      {/* Green header with progress bar and title */}
      <div className="w-full bg-gradient-to-br from-[#184C43] to-[#256d5a] rounded-b-3xl px-4 pt-8 pb-10 md:pb-14 text-white relative overflow-hidden flex flex-col items-center">
        {/* Decorative icons */}
        <div className="absolute left-8 top-16 opacity-40 z-0">
          <svg width="32" height="32">
            <circle cx="16" cy="16" r="2" fill="#B6CFC7" />
            <line x1="16" y1="0" x2="16" y2="32" stroke="#B6CFC7" strokeWidth="1.5" />
            <line x1="0" y1="16" x2="32" y2="16" stroke="#B6CFC7" strokeWidth="1.5" />
          </svg>
        </div>
        <div className="absolute right-8 top-10 opacity-40 z-0">
          <svg width="32" height="32">
            <circle cx="16" cy="16" r="2" fill="#B6CFC7" />
            <line x1="16" y1="0" x2="16" y2="32" stroke="#B6CFC7" strokeWidth="1.5" />
            <line x1="0" y1="16" x2="32" y2="16" stroke="#B6CFC7" strokeWidth="1.5" />
          </svg>
        </div>
        {/* Progress bar */}
        <div className="w-full max-w-lg mx-auto mb-6">
          <div className="flex justify-between mb-2">
            <span className="text-sm text-white/70">Uploaded in our database</span>
            <span className="text-sm text-white/80 font-medium">{question.progress}%</span>
          </div>
          <div className="h-2 bg-white/20 rounded-full overflow-hidden">
            <div className="h-full bg-[#c6e48b]" style={{ width: `${question.progress}%` }}></div>
          </div>
        </div>
        {/* Title and subtitle */}
        <div className="w-full max-w-lg mx-auto text-center z-10">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-3 leading-tight">
            {question.title}
          </h2>
          <p className="text-white/70 text-base md:text-lg mb-2">
            {question.subheading}
          </p>
        </div>
      </div>
      {/* Card with features */}
      <div className="flex-1 w-full flex flex-col items-center bg-white px-4 pt-8 pb-16">
        <div
          className={`w-full max-w-md mx-auto bg-[#F5F5F5] rounded-3xl flex flex-col items-center justify-center p-8 shadow transition-all duration-700 ${cardVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}
        >
          <div className="text-center text-[#184C43] font-bold text-xl mb-2">{question.cardTitle}</div>
          <div className="text-center text-[#6B7A7A] text-sm mb-6">
            {question.cardSub}
          </div>
          <div className="w-full flex flex-col gap-4 mb-6">
            {features.map((feature, idx) => (
              <div
                key={feature.title}
                className={`w-full bg-white rounded-xl p-5 flex items-start gap-4 shadow-sm transition-opacity duration-700 ${visibleFeatures > idx ? 'opacity-100' : 'opacity-0'}`}
              >
                <div className="flex-shrink-0 mt-1">{feature.icon}</div>
                <div>
                  <div className="font-semibold text-[#184C43] mb-1">{feature.title}</div>
                  <div className="text-[#184C43]/80 text-sm">{feature.desc}</div>
                </div>
              </div>
            ))}
          </div>
          <div className="w-full text-center text-[#184C43]/70 text-xs mt-2 mb-4">
            <svg width="18" height="18" fill="none" viewBox="0 0 18 18" className="inline-block mr-1 align-middle"><circle cx="9" cy="9" r="8" stroke="#184C43" strokeWidth="1.5"/><path d="M5 9l2 2 4-4" stroke="#184C43" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/></svg>
            Join <b>thousands</b> of satisfied customers who&apos;ve transformed their skin with lymph
          </div>
        </div>
        <button
          className="mt-8 w-full max-w-lg py-4 px-6 rounded-full font-medium transition-all flex items-center justify-center text-lg bg-[#c6e48b] text-[#184C43] hover:bg-[#b6d97a]"
          onClick={handleCta}
        >
          Let&apos;s Begin with your Lifestyle Assessment →
        </button>
      </div>
    </div>
  );
} 