"use client";
import React, { useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { useSelector } from "react-redux";
import { RootState } from "@/app/redux/store";
import { quizData } from "@/data/quiz-data";
import useFacebookPixel from "@/hooks/useFacebookPixel";

const question = quizData["thank-you"];

export default function QuizThankYouPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const userEmail = useSelector((state: RootState) => state.lymph.userEmail);

  // Facebook Pixel tracking
  const { trackPurchase } = useFacebookPixel();

  // Plan data for tracking
  const plans = [
    {
      id: "7day",
      label: "7-Day Plan",
      price: 13.99,
      contentIds: ["LymphDrainage-7day"],
    },
    {
      id: "1month",
      label: "1-Month Plan",
      price: 19.99,
      contentIds: ["LymphDrainage-1month"],
    },
    {
      id: "3month",
      label: "3-Month Plan",
      price: 29.99,
      contentIds: ["LymphDrainage-3month"],
    },
  ];

  useEffect(() => {
    const trackPurchaseEvent = async () => {
      console.log("💰 Thank you page loaded - tracking purchase event");

      // Try to get purchase details from URL parameters or localStorage
      const planId =
        searchParams?.get("plan") ||
        localStorage.getItem("selectedPlan") ||
        "3month";
      const amount =
        searchParams?.get("amount") || localStorage.getItem("purchaseAmount");
      const email =
        userEmail ||
        searchParams?.get("email") ||
        localStorage.getItem("userEmail");

      if (!email) {
        console.error("❌ No email found for purchase tracking");
        return;
      }

      // Get plan details
      const planDetails = plans.find((p) => p.id === planId) || plans[2]; // Default to 3month
      const purchaseAmount = amount ? Number(amount) : planDetails.price;

      console.log("📊 Purchase details:", {
        email: email ? "[REDACTED]" : "none",
        planId,
        amount: purchaseAmount,
        contentIds: planDetails.contentIds,
      });

      // Track Purchase event
      try {
        await trackPurchase(
          purchaseAmount,
          "USD",
          planDetails.contentIds,
          "subscription",
          email
        );
        console.log("✅ Purchase event tracked successfully:", {
          plan: planDetails.label,
          amount: purchaseAmount,
          currency: "USD",
        });
      } catch (error) {
        console.error("❌ Failed to track Purchase event:", error);
      }

      // Clean up localStorage after tracking
      localStorage.removeItem("selectedPlan");
      localStorage.removeItem("purchaseAmount");
    };

    // Track purchase event after component mounts
    trackPurchaseEvent();
  }, [searchParams, userEmail, trackPurchase, plans]);

  return (
    <div className="flex flex-col min-h-screen bg-white">
      {/* Header */}
      <div className="w-full bg-gradient-to-br from-[#0F403D] via-[#0E6661] to-[#0F403D] rounded-b-3xl px-6 pt-8 pb-10 text-white relative overflow-hidden flex flex-col items-center">
        {/* Decorative icons */}
        <div className="absolute left-8 top-16 opacity-40 z-0">
          <svg width="32" height="32">
            <circle cx="16" cy="16" r="2" fill="#B6CFC7" />
            <line
              x1="16"
              y1="0"
              x2="16"
              y2="32"
              stroke="#B6CFC7"
              strokeWidth="1.5"
            />
            <line
              x1="0"
              y1="16"
              x2="32"
              y2="16"
              stroke="#B6CFC7"
              strokeWidth="1.5"
            />
          </svg>
        </div>
        <div className="absolute right-8 top-10 opacity-40 z-0">
          <svg width="32" height="32">
            <circle cx="16" cy="16" r="2" fill="#B6CFC7" />
            <line
              x1="16"
              y1="0"
              x2="16"
              y2="32"
              stroke="#B6CFC7"
              strokeWidth="1.5"
            />
            <line
              x1="0"
              y1="16"
              x2="32"
              y2="16"
              stroke="#B6CFC7"
              strokeWidth="1.5"
            />
          </svg>
        </div>
        <h2 className="text-3xl md:text-4xl font-bold text-white mb-3 leading-tight">
          {question.title}
        </h2>
        <p className="text-white/70 text-base md:text-lg mb-2">
          {question.subheading}
        </p>
      </div>

      {/* Main Content */}
      <div className="w-full flex justify-center mt-8 mb-4 px-6">
        <div className="bg-[#F5F5F5] rounded-3xl p-6 flex flex-col items-center max-w-md w-full">
          <div className="font-bold text-lg text-[#184C43] mb-2">
            {question.cardTitle}
          </div>
          <div className="text-sm text-[#184C43]/80 mb-6 text-center">
            {question.cardSub}
          </div>

          <div className="w-full flex flex-col gap-3 mb-6">
            {question.bulletPoints?.map((point, index) => (
              <div
                key={index}
                className="flex items-center gap-3 bg-white rounded-xl p-3"
              >
                <div className="w-6 h-6 bg-[#184C43] rounded-full flex items-center justify-center">
                  <svg width="12" height="12" viewBox="0 0 16 16" fill="none">
                    <path
                      d="M13.3333 4L6 11.3333L2.66667 8"
                      stroke="white"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                  </svg>
                </div>
                <div className="text-sm text-[#184C43]">{point}</div>
              </div>
            ))}
          </div>

          <button
            onClick={() => router.push("/")}
            className="w-full bg-[#184C43] text-white rounded-xl px-8 py-3 font-semibold hover:bg-[#256d5a] transition-colors"
          >
            Go to Home
          </button>
        </div>
      </div>
    </div>
  );
}
