import { quizData } from "@/data/quiz-data";
import ReferralNamePage from "@/components/ReferralNamePage";

// This tells Next.js to pre-render this page at build time
export const dynamic = 'force-static';

// This ensures the page is statically generated
export const revalidate = false;

export default function ReferralNamePageWrapper() {
  return <ReferralNamePage question={quizData["referralname"]} />;
} 