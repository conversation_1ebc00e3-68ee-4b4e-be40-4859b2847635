"use client";
import React, { useEffect, useState } from "react";
import { ArrowRight } from "lucide-react";
import { useRouter } from "next/navigation";
import { LymphQuizProvider, useLymphQuiz } from "../LymphQuizContext";
import { quizData } from "@/data/quiz-data";
import Image from "next/image";

const question = quizData["current-lifestyle"];

function CurrentLifestyleContent() {
  const [visibleBullets, setVisibleBullets] = useState(1);
  const [showFinalState, setShowFinalState] = useState(false);
  const router = useRouter();

  useEffect(() => {
    // Animate bullet points
    const bulletInterval = setInterval(() => {
      setVisibleBullets((prev) => {
        if (prev < (question.bulletPoints?.length ?? 0)) {
          return prev + 1;
        } else {
          // Show final state after all bullets are visible
          setShowFinalState(true);
          clearInterval(bulletInterval);
          return prev;
        }
      });
    }, 900);
    return () => {
      clearInterval(bulletInterval);
    };
  }, []);

  const handleNext = () => {
    router.push(question.nextPage);
  };

  // Determine which SVG to show based on visibleBullets and final state
  let svgSrc = "/images/current-lifestyle.svg";
  if (showFinalState) {
    svgSrc = "/images/current-lifestyle-3.svg";
  } else if (visibleBullets >= 3) {
    svgSrc = "/images/current-lifestyle-2.svg";
  } else if (visibleBullets === 2) {
    svgSrc = "/images/current-lifestyle-1.svg";
  }

  return (
    <div className="flex flex-col min-h-screen bg-white">
      {/* Green header with progress bar and title */}
      <div className="w-full bg-gradient-to-br from-[#0F403D] via-[#0E6661] to-[#0F403D] rounded-b-3xl px-4 pt-8 pb-10 md:pb-14 text-white relative overflow-hidden flex flex-col items-center">
        {/* Decorative icons */}
        <div className="absolute left-8 top-16 opacity-40 z-0">
          <svg width="32" height="32">
            <circle cx="16" cy="16" r="2" fill="#B6CFC7" />
            <line x1="16" y1="0" x2="16" y2="32" stroke="#B6CFC7" strokeWidth="1.5" />
            <line x1="0" y1="16" x2="32" y2="16" stroke="#B6CFC7" strokeWidth="1.5" />
          </svg>
        </div>
        <div className="absolute right-8 top-10 opacity-40 z-0">
          <svg width="32" height="32">
            <circle cx="16" cy="16" r="2" fill="#B6CFC7" />
            <line x1="16" y1="0" x2="16" y2="32" stroke="#B6CFC7" strokeWidth="1.5" />
            <line x1="0" y1="16" x2="32" y2="16" stroke="#B6CFC7" strokeWidth="1.5" />
          </svg>
        </div>
        {/* Progress bar */}
        <div className="w-full max-w-lg mx-auto mb-6">
          <div className="flex justify-between mb-2">
            <span className="text-sm text-white/70">Analysing</span>
            <span className="text-sm text-white/80 font-medium">{question.progress}%</span>
          </div>
          <div className="h-2 bg-white/20 rounded-full overflow-hidden">
            <div className="h-full bg-[#c6e48b] transition-all duration-700" style={{ width: `${question.progress}%` }}></div>
          </div>
        </div>
        {/* Title and subtitle */}
        <div className="w-full max-w-lg mx-auto text-center z-10">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-3 leading-tight">
            {question.title}
          </h2>
          <p className="text-white/70 text-base md:text-lg mb-2">
            {question.subheading.split('\n')[0]}
          </p>
          <p className="text-white/70 text-base md:text-lg mb-2">
            {question.subheading.split('\n')[1]}
          </p>
        </div>
      </div>
      {/* Animated card */}
      <div className="flex-1 w-full flex flex-col items-center bg-white px-4 pt-8 pb-16">
        <div className="w-full max-w-md mx-auto bg-[#F5F5F5] rounded-3xl flex flex-col items-center justify-center p-8 shadow transition-all duration-500">
          {/* Animated SVG illustration */}
          <div className="flex items-center justify-center mb-6 transition-all duration-700" style={{ width: 96, height: 96 }}>
            <Image
              src={svgSrc}
              alt="Lifestyle Analysis Illustration"
              width={96}
              height={96}
              className="object-contain transition-opacity duration-700"
              priority
            />
          </div>
          <div className="text-center text-[#184C43] font-bold text-xl mb-2">{question.cardTitle}</div>
          <div className="text-center text-[#6B7A7A] text-sm mb-6">
            {question.cardSub}
          </div>
          <ul className="text-[#184C43] text-base space-y-2 w-full">
            {question.bulletPoints?.map((point, idx) => (
              <li
                key={idx}
                className={`flex items-start transition-opacity duration-700 ${visibleBullets > idx ? 'opacity-100' : 'opacity-0'}`}
              >
                <span className="mr-2 mt-1">✱</span> {point}
              </li>
            ))}
          </ul>
        </div>
        <button
          className={`mt-8 w-full max-w-lg py-4 px-6 rounded-full font-medium transition-all flex items-center justify-center text-lg bg-[#c6e48b] text-[#184C43] hover:bg-[#b6d97a] ${showFinalState ? 'opacity-100' : 'opacity-0'}`}
          onClick={handleNext}
        >
          Next <ArrowRight className="ml-2 h-5 w-5" />
        </button>
      </div>
    </div>
  );
}

export default function CurrentLifestylePage() {
  return (
    <LymphQuizProvider>
      <CurrentLifestyleContent />
    </LymphQuizProvider>
  );
} 