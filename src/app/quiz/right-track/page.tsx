"use client";
import React, { useEffect, useState } from "react";
import { quizData } from "@/data/quiz-data";

const skinResults = [
  { text: <><b>Gentle support</b> needed for sustained results</>, checked: true },
  { text: <>Focus on <b>deep collagen</b> care</>, checked: true },
  { text: <>Daily de-puffing and detox required</>, checked: true },
  { text: <>Stress-relief & sculpting techniques recommended</>, checked: true },
];

const supportLevels = [
  { label: "Collagen Support", value: 85 },
  { label: "Firmness", value: 70 },
  { label: "De-puffing", value: 90 },
];

export default function RightTrackPage() {
  const [cardVisible, setCardVisible] = useState(false);
  const [visibleChecks, setVisibleChecks] = useState(0);
  const [barPercents, setBarPercents] = useState([0, 0, 0]);
  const question = quizData["right-track"];

  useEffect(() => {
    // Fade in card
    const cardTimeout = setTimeout(() => setCardVisible(true), 400);
    // Animate checkmarks
    const checkInterval = setInterval(() => {
      setVisibleChecks((prev) => {
        if (prev < skinResults.length) return prev + 1;
        clearInterval(checkInterval);
        return prev;
      });
    }, 400);
    // Animate progress bars
    const barIntervals: NodeJS.Timeout[] = [];
    supportLevels.forEach((item, idx) => {
      barIntervals[idx] = setTimeout(() => {
        setBarPercents((prev) => {
          const next = [...prev];
          next[idx] = item.value;
          return next;
        });
      }, 1200 + idx * 400);
    });
    return () => {
      clearTimeout(cardTimeout);
      clearInterval(checkInterval);
      barIntervals.forEach(clearTimeout);
    };
  }, []);

  const handleCta = () => {
    window.location.href = question.nextPage;
  };

  return (
    <div className="flex flex-col min-h-screen bg-white">
      {/* Green header with progress bar and title */}
      <div className="w-full bg-gradient-to-br from-[#184C43] to-[#256d5a] rounded-b-3xl px-4 pt-8 pb-10 md:pb-14 text-white relative overflow-hidden flex flex-col items-center">
        {/* Decorative icons */}
        <div className="absolute left-8 top-16 opacity-40 z-0">
          <svg width="32" height="32">
            <circle cx="16" cy="16" r="2" fill="#B6CFC7" />
            <line x1="16" y1="0" x2="16" y2="32" stroke="#B6CFC7" strokeWidth="1.5" />
            <line x1="0" y1="16" x2="32" y2="16" stroke="#B6CFC7" strokeWidth="1.5" />
          </svg>
        </div>
        <div className="absolute right-8 top-10 opacity-40 z-0">
          <svg width="32" height="32">
            <circle cx="16" cy="16" r="2" fill="#B6CFC7" />
            <line x1="16" y1="0" x2="16" y2="32" stroke="#B6CFC7" strokeWidth="1.5" />
            <line x1="0" y1="16" x2="32" y2="16" stroke="#B6CFC7" strokeWidth="1.5" />
          </svg>
        </div>
        {/* Progress bar */}
        <div className="w-full max-w-lg mx-auto mb-6">
          <div className="flex justify-between mb-2">
            <span className="text-sm text-white/70">Uploaded in our database</span>
            <span className="text-sm text-white/80 font-medium">{question.progress}%</span>
          </div>
          <div className="h-2 bg-white/20 rounded-full overflow-hidden">
            <div className="h-full bg-[#c6e48b]" style={{ width: `${question.progress}%` }}></div>
          </div>
        </div>
        {/* Title and subtitle */}
        <div className="w-full max-w-lg mx-auto text-center z-10">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-3 leading-tight">
            {question.title}
          </h2>
          <p className="text-white/70 text-base md:text-lg mb-2">
            {question.subheading}
          </p>
        </div>
      </div>
      {/* Card with results and support levels */}
      <div className="flex-1 w-full flex flex-col items-center bg-white px-4 pt-8 pb-16">
        <div
          className={`w-full max-w-md mx-auto bg-[#F5F5F5] rounded-3xl flex flex-col items-center justify-center p-8 shadow transition-all duration-700 ${cardVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}
        >
          {/* Section title */}
          <div className="w-full text-center text-[#184C43] font-semibold text-lg mb-6 flex flex-col items-center">
            <span className="inline-flex items-center mb-2">
              <svg width="20" height="20" fill="none" viewBox="0 0 20 20" className="mr-2"><circle cx="10" cy="10" r="9" stroke="#184C43" strokeWidth="1.5"/><text x="10" y="15" textAnchor="middle" fontSize="14" fill="#184C43">✱</text></svg>
              Based on your answers, here&apos;s what happens next:
            </span>
          </div>
          {/* Skin Analysis Results */}
          <div className="w-full bg-white rounded-xl p-5 mb-6 shadow-sm">
            <div className="font-semibold text-[#184C43] mb-3 flex items-center">
              <svg width="18" height="18" fill="none" viewBox="0 0 18 18" className="mr-2"><rect x="2" y="2" width="14" height="14" rx="4" fill="#c6e48b"/><path d="M6 9.5l2 2 4-4" stroke="#184C43" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/></svg>
              Skin Analysis Results
            </div>
            <ul className="text-[#184C43] text-sm space-y-2">
              {skinResults.map((item, idx) => (
                <li key={idx} className={`flex items-start transition-opacity duration-700 ${visibleChecks > idx ? 'opacity-100' : 'opacity-0'}`}>
                  <svg width="16" height="16" fill="none" viewBox="0 0 16 16" className="mt-1 mr-2"><circle cx="8" cy="8" r="7" stroke="#c6e48b" strokeWidth="1.2"/><path d="M5 9l2 2 4-4" stroke="#184C43" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/></svg>
                  <span>{item.text}</span>
                </li>
              ))}
            </ul>
          </div>
          {/* Targeted Support Levels */}
          <div className="w-full bg-white rounded-xl p-5 mb-6 shadow-sm">
            <div className="font-semibold text-[#184C43] mb-3 flex items-center">
              <svg width="18" height="18" fill="none" viewBox="0 0 18 18" className="mr-2"><circle cx="9" cy="9" r="8" stroke="#184C43" strokeWidth="1.5"/><text x="9" y="14" textAnchor="middle" fontSize="12" fill="#184C43">%</text></svg>
              Targeted Support Levels
            </div>
            <ul className="text-[#184C43] text-sm space-y-4">
              {supportLevels.map((item, idx) => (
                <li key={idx} className="flex flex-col">
                  <div className="flex justify-between mb-1">
                    <span>{item.label}</span>
                    <span className="font-semibold">{barPercents[idx]}%</span>
                  </div>
                  <div className="w-full h-2 bg-[#E5E5E5] rounded-full overflow-hidden">
                    <div className="h-full bg-[#c6e48b] transition-all duration-700" style={{ width: `${barPercents[idx]}%` }}></div>
                  </div>
                </li>
              ))}
            </ul>
          </div>
          <div className="text-center text-[#184C43] font-bold text-xl mb-2">We&apos;re Understanding<br />How You Live…</div>
          <div className="text-center text-[#6B7A7A] text-sm mb-6">
            Connecting your daily life to your skin&apos;s wellness!
          </div>
          <div className="w-full text-center text-[#184C43]/70 text-xs mt-2 mb-4">
            Let&apos;s protect your <b>results</b> & elevate your self-care with Lymph!
          </div>
        </div>
        <button
          className="mt-8 w-full max-w-lg py-4 px-6 rounded-full font-medium transition-all flex items-center justify-center text-lg bg-[#c6e48b] text-[#184C43] hover:bg-[#b6d97a]"
          onClick={handleCta}
        >
          Let&apos;s Begin with your Lifestyle Assessment →
        </button>
      </div>
    </div>
  );
} 