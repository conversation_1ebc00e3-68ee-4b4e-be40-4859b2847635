"use client";
import React, { useState } from "react";
import { useRouter } from "next/navigation";
import { quizData } from "@/data/quiz-data";
import { useLymphQuizActions } from "@/app/redux/lymphHooks";
import { useSelector, useDispatch } from "react-redux";
import { RootState } from "@/app/redux/store";
import { setCustomerId } from "@/app/redux/customerSlice";
import useFacebookPixel from "@/hooks/useFacebookPixel";

const question = quizData["plan"];

export default function PlanPage() {
  const router = useRouter();
  const [email, setEmail] = useState("");
  const [optIn, setOptIn] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const dispatch = useDispatch();

  // Get Redux actions and state
  const { saveUserEmail, updateCheckoutStep } = useLymphQuizActions();
  const userEmail = useSelector((state: RootState) => state.lymph.userEmail);
  const existingCustomerId = useSelector(
    (state: RootState) => state.customer.id
  );

  // Facebook Pixel tracking
  const { trackAddToCart } = useFacebookPixel();

  // Initialize email from Redux if available
  React.useEffect(() => {
    if (userEmail) {
      setEmail(userEmail);
    }
  }, [userEmail]);

  const createStripeCustomer = async (userEmail: string) => {
    try {
      console.log("🔄 Creating Stripe customer for email:", userEmail);
      const customerResponse = await fetch("/api/customer", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ email: userEmail }),
      });

      if (!customerResponse.ok) {
        throw new Error("Failed to create/retrieve customer");
      }

      const { customer } = await customerResponse.json();
      console.log("✅ Stripe customer created/retrieved:", customer.id);

      // Store customer ID in Redux
      dispatch(setCustomerId(customer.id));
      console.log("💾 Customer ID stored in Redux:", customer.id);

      return customer.id;
    } catch (error) {
      console.error("❌ Error creating Stripe customer:", error);
      throw error;
    }
  };

  const handleLockIn = async () => {
    if (email) {
      setIsLoading(true);

      try {
        console.log("🎯 User entered email:", email);
        console.log("📧 Triggering AddToCart tracking for email collection");

        // Save email to Redux state
        saveUserEmail(email);

        // Also store email in localStorage for purchase tracking persistence
        localStorage.setItem("userEmail", email);
        console.log("💾 Email stored in localStorage for tracking persistence");

        // Create Stripe customer if not already exists
        let customerId = existingCustomerId;
        if (!customerId) {
          customerId = await createStripeCustomer(email);
        } else {
          console.log("✅ Using existing customer ID from Redux:", customerId);
        }

        // Track AddToCart event when user provides email
        try {
          await trackAddToCart(
            email,
            ["LymphDrainage"], // contentIds
            "subscription", // contentType
            29.99, // value
            "USD" // currency
          );
          console.log(
            "✅ AddToCart event triggered successfully for email:",
            email
          );
        } catch (error) {
          console.error("❌ Failed to track AddToCart event:", error);
          // Don't block the user flow if tracking fails
        }

        // Update checkout step in Redux
        updateCheckoutStep("offer");

        // Navigate to next page
        router.push(question.nextPage);
      } catch (error) {
        console.error("❌ Error in handleLockIn:", error);
        // Still allow user to proceed even if customer creation fails
        // They can retry customer creation on the offer page
        updateCheckoutStep("offer");
        router.push(question.nextPage);
      } finally {
        setIsLoading(false);
      }
    }
  };

  const handleSkip = () => {
    console.log("⏭️ User skipped email entry");
    // Update checkout step in Redux even if skipping
    updateCheckoutStep("offer");
    router.push(question.nextPage);
  };

  return (
    <div className="flex flex-col min-h-screen bg-white">
      {/* Green header with progress bar and title */}
      <div className="w-full bg-gradient-to-br from-[#0F403D] via-[#0E6661] to-[#0F403D] rounded-b-3xl px-4 pt-8 pb-10 md:pb-14 text-white relative overflow-hidden flex flex-col items-center">
        {/* Decorative icons */}
        <div className="absolute left-8 top-16 opacity-40 z-0">
          <svg width="32" height="32">
            <circle cx="16" cy="16" r="2" fill="#B6CFC7" />
            <line
              x1="16"
              y1="0"
              x2="16"
              y2="32"
              stroke="#B6CFC7"
              strokeWidth="1.5"
            />
            <line
              x1="0"
              y1="16"
              x2="32"
              y2="16"
              stroke="#B6CFC7"
              strokeWidth="1.5"
            />
          </svg>
        </div>
        <div className="absolute right-8 top-10 opacity-40 z-0">
          <svg width="32" height="32">
            <circle cx="16" cy="16" r="2" fill="#B6CFC7" />
            <line
              x1="16"
              y1="0"
              x2="16"
              y2="32"
              stroke="#B6CFC7"
              strokeWidth="1.5"
            />
            <line
              x1="0"
              y1="16"
              x2="32"
              y2="16"
              stroke="#B6CFC7"
              strokeWidth="1.5"
            />
          </svg>
        </div>
        {/* Progress bar */}
        <div className="w-full max-w-lg mx-auto mb-6">
          <div className="flex justify-between mb-2">
            <span className="text-sm text-white/70"> </span>
            <span className="text-sm text-white/80 font-medium">
              {question.progress}%
            </span>
          </div>
          <div className="h-2 bg-white/20 rounded-full overflow-hidden">
            <div
              className="h-full bg-[#c6e48b]"
              style={{ width: `${question.progress}%` }}
            ></div>
          </div>
        </div>
        {/* Title and subtitle */}
        <div className="w-full max-w-lg mx-auto text-center z-10">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-3 leading-tight">
            {question.title}
          </h2>
          <p className="text-white/70 text-base md:text-lg mb-2">
            {question.subheading}
          </p>
        </div>
      </div>
      {/* Card with claim form */}
      <div className="flex-1 w-full flex flex-col items-center bg-white px-4 pt-8 pb-16">
        <div className="w-full max-w-md mx-auto bg-[#F5F5F5] rounded-3xl flex flex-col items-center justify-center p-8 shadow transition-all duration-500">
          <div className="text-center text-[#184C43] font-bold text-xl mb-2">
            {question.cardTitle}
          </div>
          <div className="text-center text-[#6B7A7A] text-sm mb-6">
            {question.cardSub}
          </div>
          {/* Email input */}
          <div className="w-full flex items-center bg-white rounded-xl border border-[#E0E0E0] px-4 py-3 mb-4">
            <svg
              width="20"
              height="20"
              fill="none"
              viewBox="0 0 20 20"
              className="mr-2"
            >
              <rect
                x="2"
                y="4"
                width="16"
                height="12"
                rx="4"
                stroke="#184C43"
                strokeWidth="1.5"
              />
              <path
                d="M4 6l6 5 6-5"
                stroke="#184C43"
                strokeWidth="1.5"
                strokeLinecap="round"
              />
            </svg>
            <input
              type="email"
              placeholder="Your email"
              className="w-full bg-transparent outline-none text-[#184C43] text-base"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              disabled={isLoading}
            />
          </div>
          {/* Privacy assurance */}
          <div className="w-full text-center text-xs text-[#184C43] font-semibold mb-2">
            We&apos;ll NEVER share or sell your personal data
          </div>
          {/* Checkboxes */}
          <div className="w-full flex flex-col gap-2 mb-6">
            <label className="flex items-center gap-2 text-xs text-[#184C43]/80">
              <input
                type="checkbox"
                checked={optIn}
                onChange={(e) => setOptIn(e.target.checked)}
                className="accent-[#184C43]"
                disabled={isLoading}
              />
              Opt in for wellness tips, free bonuses, and app updates
            </label>
          </div>
          {/* Buttons */}
          <button
            className="w-full py-3 rounded-full font-medium text-lg bg-[#184C43] text-white mb-3 hover:bg-[#256d5a] transition-all flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed"
            onClick={handleLockIn}
            disabled={isLoading || !email}
          >
            {isLoading ? (
              <>
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                Creating Account...
              </>
            ) : (
              <>
                Lock in <span className="ml-1">✨</span>
              </>
            )}
          </button>
          <div className="text-center text-xs text-[#6B7A7A] mt-6">
            {question.cardFooter}
          </div>
        </div>
      </div>
    </div>
  );
}
