'use client'

import { createContext, useContext, useReducer, ReactNode, useEffect } from 'react';

interface QuizState {
    gender: string;
    currentQuestion: number;
    answers: Record<string, string[]>;
}

type QuizAction =
    | { type: 'SET_GENDER'; payload: string }
    | { type: 'SET_ANSWER'; payload: { questionId: string; answers: string[] } }
    | { type: 'SET_CURRENT_QUESTION'; payload: number }
    | { type: 'LOAD_STATE'; payload: QuizState };

const QuizContext = createContext<{
    state: QuizState;
    dispatch: React.Dispatch<QuizAction>;
} | undefined>(undefined);

const initialState: QuizState = {
    gender: '',
    currentQuestion: 1,
    answers: {},
};

const loadState = (): QuizState => {
    if (typeof window === 'undefined') return initialState;

    const savedState = localStorage.getItem('quizState');
    return savedState ? JSON.parse(savedState) : initialState;
};

function quizReducer(state: QuizState, action: QuizAction): QuizState {
    let newState: QuizState;

    switch (action.type) {
        case 'SET_GENDER':
            newState = { ...state, gender: action.payload };
            break;
        case 'SET_ANSWER':
            newState = {
                ...state,
                answers: {
                    ...state.answers,
                    [action.payload.questionId]: action.payload.answers,
                },
            };
            break;
        case 'SET_CURRENT_QUESTION':
            newState = { ...state, currentQuestion: action.payload };
            break;
        case 'LOAD_STATE':
            newState = action.payload;
            break;
        default:
            return state;
    }

    
    if (typeof window !== 'undefined') {
        localStorage.setItem('quizState', JSON.stringify(newState));
    }

    return newState;
}

export function QuizProvider({ children }: { children: ReactNode }) {
    const [state, dispatch] = useReducer(quizReducer, initialState);

    useEffect(() => {
        const savedState = loadState();
        dispatch({ type: 'LOAD_STATE', payload: savedState });
    }, []);

    return (
        <QuizContext.Provider value={{ state, dispatch }}>
            {children}
        </QuizContext.Provider>
    );
}

export const useQuiz = () => {
    const context = useContext(QuizContext);
    if (!context) {
        throw new Error('useQuiz must be used within a QuizProvider');
    }
    return context;
};