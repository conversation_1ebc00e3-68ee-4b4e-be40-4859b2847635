'use client'

import { Provider } from 'react-redux';
import { PersistGate } from 'redux-persist/integration/react';
import { store, persistor } from './redux/store';
import { LymphQuizProvider } from './quiz/LymphQuizContext';

export function Providers({ children }: { children: React.ReactNode }) {
    return (
        <Provider store={store}>
            <PersistGate loading={null} persistor={persistor}>
                {/* ColonFit provider (commented out) */}
                {/* <QuizProvider> */}
                    <LymphQuizProvider>
                        {children}
                    </LymphQuizProvider>
                {/* </QuizProvider> */}
            </PersistGate>
        </Provider>
    );
}
