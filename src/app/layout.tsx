import type { Metadata } from "next";
import localFont from "next/font/local";
import Script from "next/script";
import "./globals.css";
import { Providers } from "./providers";

const recoleta = localFont({
  src: "./fonts/Recoleta-Medium.ttf",
  variable: "--font-recoleta",
});

const amiko = localFont({
  src: "./fonts/Amiko-Regular.ttf",
  variable: "--font-amiko",
});

export const metadata: Metadata = {
  title: "Lymph | Your new favourite Lymphatic Health App",
  description:
    "<PERSON>ymph helps you improve your lymphatic health with science-backed routines, expert guidance, and personalized plans.",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <head>
        <Script
          id="facebook-pixel"
          strategy="afterInteractive"
          dangerouslySetInnerHTML={{
            __html: `
              !function(f,b,e,v,n,t,s)
              {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
              n.callMethod.apply(n,arguments):n.queue.push(arguments)};
              if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
              n.queue=[];t=b.createElement(e);t.async=!0;
              t.src=v;s=b.getElementsByTagName(e)[0];
              s.parentNode.insertBefore(t,s)}(window, document,'script',
              'https://connect.facebook.net/en_US/fbevents.js');
              // Pixel initialization is now handled by useFacebookPixel hook
            `,
          }}
        />
        <noscript>
          {/* eslint-disable-next-line @next/next/no-img-element */}
          <img
            height="1"
            width="1"
            style={{ display: "none" }}
            src={`https://www.facebook.com/tr?id=${process.env.NEXT_PUBLIC_FACEBOOK_PIXEL_ID}&ev=PageView&noscript=1`}
            alt=""
          />
        </noscript>
        {/* SEO & Social Meta Tags for Lymph */}
        <title>Lymph | Your new favourite Lymphatic Health App</title>
        <meta
          name="description"
          content="Lymph helps you improve your lymphatic health with science-backed routines, expert guidance, and personalized plans."
        />
        {/* Open Graph tags */}
        <meta
          property="og:title"
          content="Lymph | Your new favourite Lymphatic Health App"
        />
        <meta
          property="og:description"
          content="Lymph helps you improve your lymphatic health with science-backed routines, expert guidance, and personalized plans."
        />
        <meta property="og:url" content="https://lymph-app.vercel.app/" />
        <meta property="og:type" content="website" />
        {/* Twitter Card tags */}
        <meta name="twitter:card" content="summary_large_image" />
        <meta
          name="twitter:title"
          content="Lymph | Your new favourite Lymphatic Health App"
        />
        <meta
          name="twitter:description"
          content="Lymph helps you improve your lymphatic health with science-backed routines, expert guidance, and personalized plans."
        />
        {/* Canonical link */}
        <link rel="canonical" href="https://lymph-app.vercel.app/" />
      </head>
      <body
        className={`${recoleta.variable} ${amiko.variable} font-amiko antialiased`}
      >
        <Providers>{children}</Providers>
      </body>
    </html>
  );
}
