import { createSlice, PayloadAction } from '@reduxjs/toolkit';

// Define the state structure for Lymph quiz and user data
export interface LymphState {
  // Quiz answers from all questions
  quizAnswers: Record<string, string[]>;
  
  // User information
  userEmail: string;
  firstName: string;
  lastName: string;
  
  // Plan selection
  selectedPlan: string;
  
  // Recommendations from backend
  recommendedProducts: Array<{
    id: string;
    label: string;
    price: number;
    originalPrice: number;
    discount: number;
    isSubscription: boolean;
    isBestValue?: boolean;
  }>;
  
  // Focus areas selected by user
  focusAreas: string[];
  
  // Checkout related
  checkoutStep: 'quiz' | 'plan' | 'offer' | 'shipping' | 'payment' | 'confirmation';
}

const initialState: LymphState = {
  quizAnswers: {},
  userEmail: '',
  firstName: '',
  lastName: '',
  selectedPlan: '',
  recommendedProducts: [],
  focusAreas: [],
  checkoutStep: 'quiz'
};

export const lymphSlice = createSlice({
  name: 'lymph',
  initialState,
  reducers: {
    // Quiz answer management
    setQuizAnswer: (
      state, 
      action: PayloadAction<{ questionId: string; answers: string[] }>
    ) => {
      console.log('Reducer handling setQuizAnswer:', action.payload);
      const { questionId, answers } = action.payload;
      state.quizAnswers[questionId] = answers;
    },
    
    setAllQuizAnswers: (
      state, 
      action: PayloadAction<Record<string, string[]>>
    ) => {
      console.log('Reducer handling setAllQuizAnswers:', action.payload);
      if (action.payload && Object.keys(action.payload).length > 0) {
        state.quizAnswers = action.payload;
      }
    },
    
    // User information
    setUserEmail: (state, action: PayloadAction<string>) => {
      state.userEmail = action.payload;
    },
    
    setUserName: (
      state, 
      action: PayloadAction<{ firstName: string; lastName: string }>
    ) => {
      state.firstName = action.payload.firstName;
      state.lastName = action.payload.lastName;
    },
    
    // Plan selection
    setSelectedPlan: (state, action: PayloadAction<string>) => {
      state.selectedPlan = action.payload;
    },
    
    // Recommendations
    setRecommendedProducts: (
      state, 
      action: PayloadAction<LymphState['recommendedProducts']>
    ) => {
      state.recommendedProducts = action.payload;
    },
    
    // Focus areas
    setFocusAreas: (state, action: PayloadAction<string[]>) => {
      state.focusAreas = action.payload;
    },
    
    // Checkout flow
    setCheckoutStep: (
      state, 
      action: PayloadAction<LymphState['checkoutStep']>
    ) => {
      state.checkoutStep = action.payload;
    },
    
    // Reset state (for logout or new session)
    resetLymphState: (state) => {
      return initialState;
    }
  }
});

export const {
  setQuizAnswer,
  setAllQuizAnswers,
  setUserEmail,
  setUserName,
  setSelectedPlan,
  setRecommendedProducts,
  setFocusAreas,
  setCheckoutStep,
  resetLymphState
} = lymphSlice.actions;

export default lymphSlice.reducer;
