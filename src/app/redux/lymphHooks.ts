import { TypedUseSelectorHook, useDispatch, useSelector } from 'react-redux';
import type { RootState, AppDispatch } from './store';
import { 
  setQuizAnswer, 
  setAllQuizAnswers,
  setUserEmail,
  setSelectedPlan,
  setRecommendedProducts,
  setFocusAreas,
  setCheckoutStep,
  resetLymphState
} from './lymphSlice';
import { useCallback } from 'react';

// Basic Redux hooks with types
export const useAppDispatch = () => useDispatch<AppDispatch>();
export const useAppSelector: TypedUseSelectorHook<RootState> = useSelector;

// Lymph-specific hooks for common operations
export const useLymphQuizActions = () => {
  const dispatch = useAppDispatch();
  
  // Save answer for a specific question
  const saveQuizAnswer = useCallback((questionId: string, answers: string[]) => {
    console.log('Dispatching saveQuizAnswer action:', questionId, answers);
    dispatch(setQuizAnswer({ questionId, answers }));
  }, [dispatch]);
  
  // Save all quiz answers at once (e.g., from localStorage)
  const saveAllQuizAnswers = useCallback((answers: Record<string, string[]>) => {
    console.log('Dispatching saveAllQuizAnswers with:', answers);
    if (answers && Object.keys(answers).length > 0) {
      dispatch(setAllQuizAnswers(answers));
    }
  }, [dispatch]);
  
  // Save user email
  const saveUserEmail = useCallback((email: string) => {
    dispatch(setUserEmail(email));
  }, [dispatch]);
  
  // Select a plan
  const selectPlan = useCallback((planId: string) => {
    dispatch(setSelectedPlan(planId));
  }, [dispatch]);
  
  // Save recommended products from backend
  const saveRecommendedProducts = useCallback((products: any[]) => {
    dispatch(setRecommendedProducts(products));
  }, [dispatch]);
  
  // Save focus areas
  const saveFocusAreas = useCallback((areas: string[]) => {
    dispatch(setFocusAreas(areas));
  }, [dispatch]);
  
  // Update checkout step
  const updateCheckoutStep = useCallback((step: 'quiz' | 'plan' | 'offer' | 'shipping' | 'payment' | 'confirmation') => {
    dispatch(setCheckoutStep(step));
  }, [dispatch]);
  
  // Reset all Lymph state
  const resetQuiz = useCallback(() => {
    dispatch(resetLymphState());
  }, [dispatch]);
  
  return {
    saveQuizAnswer,
    saveAllQuizAnswers,
    saveUserEmail,
    selectPlan,
    saveRecommendedProducts,
    saveFocusAreas,
    updateCheckoutStep,
    resetQuiz
  };
};

// Hook to access Lymph state
export const useLymphState = () => {
  return useAppSelector(state => state.lymph);
};

// Hook for quiz answers specifically
export const useLymphQuizAnswers = () => {
  return useAppSelector(state => state.lymph.quizAnswers);
};

// Hook for recommended products
export const useLymphRecommendations = () => {
  return useAppSelector(state => state.lymph.recommendedProducts);
};
