import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface MetricsState {
    age: string;
    heightFt: string;
    heightIn: string;
    heightCm: string;
    weight: string;
    desiredWeight: string;
    unit: 'imperial' | 'metric';
}

const initialState: MetricsState = {
    age: '',
    heightFt: '',
    heightIn: '',
    heightCm: '',
    weight: '',
    desiredWeight: '',
    unit: 'imperial',
};

const metricsSlice = createSlice({
    name: 'metrics',
    initialState,
    reducers: {
        setMetrics: (state, action: PayloadAction<Partial<MetricsState>>) => {
            return { ...state, ...action.payload };
        },
        setUnit: (state, action: PayloadAction<'imperial' | 'metric'>) => {
            state.unit = action.payload;
        },
    },
});

export const { setMetrics, setUnit } = metricsSlice.actions;
export default metricsSlice.reducer;