import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface CustomerState {
    id: string | null;
}

const initialState: CustomerState = {
    id: null,
};

export const customerSlice = createSlice({
    name: 'customer',
    initialState,
    reducers: {
        setCustomerId: (state, action: PayloadAction<string>) => {
            console.log('Setting customer ID in slice:', action.payload);
            state.id = action.payload;
        },
    },
});

export const { setCustomerId } = customerSlice.actions;

export default customerSlice.reducer;