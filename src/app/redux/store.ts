import { configureStore } from '@reduxjs/toolkit';
import { persistStore, persistReducer } from 'redux-persist';
import storage from './storage';
import { combineReducers } from '@reduxjs/toolkit';

// Import existing reducers
import clientSecretReducer from './clientSecretSlice';
import orderReducer from './orderSlice';
import shippingReducer from './shippingslice';
import customerReducer from './customerSlice';
import metricsReducer from './metricsSlice';
import genderSlice from './genderSlice';

// Import new Lymph reducer
import lymphReducer from './lymphSlice';

const persistConfig = {
    key: 'root',
    storage,
};

const rootReducer = combineReducers({
    // Keep existing reducers to maintain compatibility
    clientSecret: clientSecretReducer,
    order: orderReducer,
    shipping: shippingReducer,
    customer: customerReducer,
    metrics: metricsReducer,
    gender: genderSlice,
    
    // Add Lymph reducer
    lymph: lymphReducer
});

const persistedReducer = persistReducer(persistConfig, rootReducer);

export const store = configureStore({
    reducer: persistedReducer,
    middleware: (getDefaultMiddleware) =>
        getDefaultMiddleware({
            serializableCheck: {
                ignoredActions: ['persist/PERSIST', 'persist/REHYDRATE']
            }
        })
});

export const persistor = persistStore(store);

// Type definitions for TypeScript
export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
