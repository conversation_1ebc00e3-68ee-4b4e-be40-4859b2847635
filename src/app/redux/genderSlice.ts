import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface GenderState {
    gender: string;
}

const initialState: GenderState = {
    gender: ''
};

const genderSlice = createSlice({
    name: 'gender',
    initialState,
    reducers: {
        setGender: (state, action: PayloadAction<string>) => {
            state.gender = action.payload;
        },
    },
});

export const { setGender } = genderSlice.actions;
export default genderSlice.reducer;