import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface OrderState {
    title: string;
    amount: number;
    quantity: number;
    isFirstSubscription: boolean;
    discountApplied: number;
    amountAfterDiscount: number;
    isSubscription: boolean; 
    productId?: string; 
}

const initialState: OrderState = {
    title: '',
    amount: 0,
    quantity: 0,
    isFirstSubscription: false,
    discountApplied: 0,
    amountAfterDiscount: 0,
    isSubscription: false, 
    productId: '' 
};

const orderSlice = createSlice({
    name: 'order',
    initialState,
    reducers: {
        setOrderDetails: (state, action: PayloadAction<Partial<OrderState>>) => {
            return { ...state, ...action.payload };
        },
    },
});

export const { setOrderDetails } = orderSlice.actions;
export default orderSlice.reducer;