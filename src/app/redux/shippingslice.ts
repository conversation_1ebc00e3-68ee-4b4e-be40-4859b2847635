import { createSlice, PayloadAction } from '@reduxjs/toolkit';

export interface ShippingState {
    firstName: string;
    lastName: string;
    streetAddress: string;
    apartment?: string;
    country: string;
    state: string;
    city: string;
    zipCode: string;
    email: string;
    phone: string;
    agreeToSMS: boolean;
}

const initialState: ShippingState = {
    firstName: '',
    lastName: '',
    streetAddress: '',
    apartment: '',
    country: '',
    state: '',
    city: '',
    zipCode: '',
    email: '',
    phone: '',
    agreeToSMS: false,
};

export const shippingSlice = createSlice({
    name: 'shipping',
    initialState,
    reducers: {
        setShippingDetails: (state, action: PayloadAction<ShippingState>) => {
            return { ...state, ...action.payload };
        },
        setAgreeToSMS: (state, action: PayloadAction<boolean>) => {
            state.agreeToSMS = action.payload;
        },
    },
});

export const { setShippingDetails, setAgreeToSMS } = shippingSlice.actions;

export default shippingSlice.reducer;