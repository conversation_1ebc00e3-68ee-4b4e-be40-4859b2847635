import { createSelector } from '@reduxjs/toolkit';
import { RootState } from './store';

// Base selector for lymph state
const selectLymphState = (state: RootState) => state.lymph;

// Select all quiz answers
export const selectQuizAnswers = createSelector(
  selectLymphState,
  (lymph) => lymph.quizAnswers
);

// Select a specific quiz answer
export const makeSelectQuizAnswer = (questionId: string) => 
  createSelector(
    selectQuizAnswers,
    (answers) => answers[questionId] || []
  );

// Select user email
export const selectUserEmail = createSelector(
  selectLymphState,
  (lymph) => lymph.userEmail
);

// Select selected plan
export const selectSelectedPlan = createSelector(
  selectLymphState,
  (lymph) => lymph.selectedPlan
);

// Select recommended products
export const selectRecommendedProducts = createSelector(
  selectLymphState,
  (lymph) => lymph.recommendedProducts
);

// Select best value product
export const selectBestValueProduct = createSelector(
  selectRecommendedProducts,
  (products) => products.find(product => product.isBestValue) || products[0]
);

// Select focus areas
export const selectFocusAreas = createSelector(
  selectLymphState,
  (lymph) => lymph.focusAreas
);

// Select checkout step
export const selectCheckoutStep = createSelector(
  selectLymphState,
  (lymph) => lymph.checkoutStep
);

// Select if user has completed quiz
export const selectHasCompletedQuiz = createSelector(
  selectLymphState,
  (lymph) => Object.keys(lymph.quizAnswers).length > 0 && lymph.userEmail !== ''
);