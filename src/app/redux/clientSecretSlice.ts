import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface ClientSecretState {
    secret: string | null;
}

const initialState: ClientSecretState = {
    secret: null,
};

const clientSecretSlice = createSlice({
    name: 'clientSecret',
    initialState,
    reducers: {
        setClientSecret: (state, action: PayloadAction<string>) => {
            state.secret = action.payload;
        },
        clearClientSecret: (state) => {
            state.secret = null;
        },
    },
});

export const { setClientSecret, clearClientSecret } = clientSecretSlice.actions;

export default clientSecretSlice.reducer;