'use client'
import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { XCircle } from 'lucide-react';

const CancelPage = () => {
    const router = useRouter();

    useEffect(() => {
        const timer = setTimeout(() => {
            router.push('/plans');
        }, 5000);

        return () => clearTimeout(timer);
    }, [router]);

    return (
        <div className="min-h-screen bg-gray-50 flex flex-col justify-center items-center px-4">
            <XCircle className="h-16 w-16 text-red-500 mb-4" />
            <h1 className="text-3xl font-bold text-gray-900 mb-2">Payment Cancelled</h1>
            <p className="text-gray-600 text-center max-w-md mb-4">
                Your payment was cancelled. Don&apos;t worry - no charges were made to your account.
            </p>

            <div className="text-gray-500">
                <span>Redirecting you back to plans page in 5 seconds...</span>
                <button
                    onClick={() => router.push('/plans')}
                    className="mt-4 px-6 py-2 bg-blue-500 text-white rounded-full hover:bg-blue-600 transition-colors duration-200"
                >
                    Go to Plans Now
                </button>
            </div>
        </div>
    );
};

export default CancelPage;