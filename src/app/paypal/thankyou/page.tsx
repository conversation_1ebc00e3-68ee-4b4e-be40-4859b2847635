"use client";

import { useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { useSelector } from "react-redux";
import { RootState } from "@/app/redux/store";
import { CheckCircle } from "lucide-react";
import useFacebookPixel from "@/hooks/useFacebookPixel";

// Separate the client content into its own component
const ThankYouContent = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const userEmail = useSelector((state: RootState) => state.lymph.userEmail);

  // Facebook Pixel tracking
  const { trackPurchase } = useFacebookPixel();

  // Plan data for tracking
  const plans = [
    {
      id: "7day",
      label: "7-Day Plan",
      price: 13.99,
      contentIds: ["LymphDrainage-7day"],
    },
    {
      id: "1month",
      label: "1-Month Plan",
      price: 19.99,
      contentIds: ["LymphDrainage-1month"],
    },
    {
      id: "3month",
      label: "3-Month Plan",
      price: 29.99,
      contentIds: ["LymphDrainage-3month"],
    },
  ];

  useEffect(() => {
    const verifyPayment = async () => {
      const token = searchParams?.get("token");
      if (!token) {
        router.push("/plans");
        return;
      }
    };

    const trackPurchaseEvent = async () => {
      console.log("💰 PayPal Thank you page loaded - tracking purchase event");

      // Try to get purchase details from URL parameters or localStorage
      const planId =
        searchParams?.get("plan") ||
        localStorage.getItem("selectedPlan") ||
        "3month";
      const amount =
        searchParams?.get("amount") || localStorage.getItem("purchaseAmount");
      const email =
        userEmail ||
        searchParams?.get("email") ||
        localStorage.getItem("userEmail");

      if (!email) {
        console.error("❌ No email found for PayPal purchase tracking");
        return;
      }

      // Get plan details
      const planDetails = plans.find((p) => p.id === planId) || plans[2]; // Default to 3month
      const purchaseAmount = amount ? Number(amount) : planDetails.price;

      console.log("📊 PayPal Purchase details:", {
        email: email ? "[REDACTED]" : "none",
        planId,
        amount: purchaseAmount,
        contentIds: planDetails.contentIds,
        token: searchParams?.get("token") ? "[REDACTED]" : "none",
      });

      // Track Purchase event
      try {
        await trackPurchase(
          purchaseAmount,
          "USD",
          planDetails.contentIds,
          "subscription",
          email
        );
        console.log("✅ PayPal Purchase event tracked successfully:", {
          plan: planDetails.label,
          amount: purchaseAmount,
          currency: "USD",
        });
      } catch (error) {
        console.error("❌ Failed to track PayPal Purchase event:", error);
      }

      // Clean up localStorage after tracking
      localStorage.removeItem("selectedPlan");
      localStorage.removeItem("purchaseAmount");
    };

    verifyPayment();
    trackPurchaseEvent();
  }, [router, searchParams, userEmail, trackPurchase, plans]);

  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-3xl mx-auto">
        <div className="text-center mb-8">
          <CheckCircle className="mx-auto h-16 w-16 text-green-500" />
          <h1 className="mt-4 text-3xl font-bold text-gray-900">
            Thank you for your order!
          </h1>
          <p className="mt-2 text-lg text-gray-600">
            We&apos;ve received your payment and will process your order
            shortly.
          </p>
        </div>

        <div className="mt-8 text-center">
          <p className="text-gray-600">
            You will receive a confirmation email shortly with your order
            details.
          </p>
        </div>
      </div>
    </div>
  );
};

export default function ThankYouPage() {
  return <ThankYouContent />;
}
