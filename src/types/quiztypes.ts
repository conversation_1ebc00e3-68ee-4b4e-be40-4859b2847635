import { StaticImageData } from 'next/image';
import React from 'react';

export interface Option {
    id: string;
    text: string;
    image?: StaticImageData;
    isExclusive?: boolean;
}

export interface BaseQuestion {
   
}

export interface StandardQuestion {
    id: string;
    question: string;
    options: Option[];
    type: 'single' | 'multiple';
}

export interface MeasurementsQuestion {
    id: string;
    type: 'measurements';
}

export type Question = StandardQuestion | MeasurementsQuestion;

export interface QuizComponentProps {
    question: string;
    options: Option[];
    type: 'single' | 'multiple';
    onNext: (selectedOptions: string[]) => void;
    onBack: () => void;
}

export interface QuestionLayoutProps {
    children: React.ReactNode;
    currentQuestion: number;
    totalQuestions: number;
    ui1?: StaticImageData;
    ui2?: StaticImageData;
}

export interface MeasurementsComponentProps {
    onNext: () => void;
    onBack: () => void;
}