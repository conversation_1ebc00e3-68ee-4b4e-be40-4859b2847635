interface PixelEvent {
  eventName: string;
  timestamp: string;
  pixelId?: string;
  email?: string;
  value?: number;
  currency?: string;
  contentIds?: string[];
  fbclid?: string;
  fbp?: string;
  fbc?: string;
  userAgent?: string;
  ipAddress?: string;
  source: "frontend" | "backend";
  status: "initiated" | "success" | "error";
  error?: string;
  metaResponse?: any;
}

class PixelLogger {
  private events: PixelEvent[] = [];
  private maxEvents = 100; // Keep last 100 events

  logEvent(event: Partial<PixelEvent>) {
    const fullEvent: PixelEvent = {
      timestamp: new Date().toISOString(),
      source: "frontend",
      status: "initiated",
      ...event,
      eventName: event.eventName || "Unknown",
    };

    this.events.unshift(fullEvent);

    // Keep only the most recent events
    if (this.events.length > this.maxEvents) {
      this.events = this.events.slice(0, this.maxEvents);
    }

    // Enhanced console logging with context
    const logPrefix = `🔍 [${fullEvent.source.toUpperCase()}] ${
      fullEvent.eventName
    }`;
    const logData = {
      timestamp: fullEvent.timestamp,
      status: fullEvent.status,
      pixelId: fullEvent.pixelId,
      email: fullEvent.email ? "[REDACTED]" : "none",
      value: fullEvent.value,
      currency: fullEvent.currency,
      contentIds: fullEvent.contentIds,
      fbclid: fullEvent.fbclid ? "[PRESENT]" : "none",
      fbp: fullEvent.fbp ? "[PRESENT]" : "none",
      fbc: fullEvent.fbc ? "[PRESENT]" : "none",
      ...(fullEvent.error && { error: fullEvent.error }),
      ...(fullEvent.metaResponse && { metaResponse: fullEvent.metaResponse }),
    };

    switch (fullEvent.status) {
      case "success":
        console.log(`✅ ${logPrefix}:`, logData);
        break;
      case "error":
        console.error(`❌ ${logPrefix}:`, logData);
        break;
      default:
        console.log(`🚀 ${logPrefix}:`, logData);
    }

    // Store in localStorage for debugging (development only)
    if (process.env.NODE_ENV === "development") {
      try {
        localStorage.setItem(
          "pixelTrackingLogs",
          JSON.stringify(this.events.slice(0, 20))
        );
      } catch (e) {
        // Ignore localStorage errors
      }
    }
  }

  getEvents(): PixelEvent[] {
    return [...this.events];
  }

  getEventsByName(eventName: string): PixelEvent[] {
    return this.events.filter((event) => event.eventName === eventName);
  }

  getRecentErrors(): PixelEvent[] {
    return this.events.filter((event) => event.status === "error").slice(0, 10);
  }

  exportLogs(): string {
    return JSON.stringify(this.events, null, 2);
  }

  clearLogs(): void {
    this.events = [];
    if (typeof window !== "undefined") {
      localStorage.removeItem("pixelTrackingLogs");
    }
  }

  // Debug helper to check tracking health
  getTrackingHealth(): {
    totalEvents: number;
    successRate: number;
    recentErrors: number;
    missingFbclid: number;
    missingCookies: number;
  } {
    const total = this.events.length;
    const successful = this.events.filter((e) => e.status === "success").length;
    const recentErrors = this.events.filter(
      (e) =>
        e.status === "error" &&
        new Date(e.timestamp) > new Date(Date.now() - 60000) // Last minute
    ).length;
    const missingFbclid = this.events.filter((e) => !e.fbclid).length;
    const missingCookies = this.events.filter((e) => !e.fbp && !e.fbc).length;

    return {
      totalEvents: total,
      successRate: total > 0 ? (successful / total) * 100 : 0,
      recentErrors,
      missingFbclid,
      missingCookies,
    };
  }
}

// Global instance
const pixelLogger = new PixelLogger();

export default pixelLogger;

// Helper functions for easy logging
export const logPixelEvent = (eventName: string, data: Partial<PixelEvent>) => {
  pixelLogger.logEvent({ eventName, ...data });
};

export const logPixelSuccess = (
  eventName: string,
  data: Partial<PixelEvent>
) => {
  pixelLogger.logEvent({ eventName, status: "success", ...data });
};

export const logPixelError = (
  eventName: string,
  error: string,
  data: Partial<PixelEvent>
) => {
  pixelLogger.logEvent({ eventName, status: "error", error, ...data });
};

// Debug function to be called from browser console
if (typeof window !== "undefined") {
  (window as any).debugPixelTracking = () => {
    const health = pixelLogger.getTrackingHealth();
    console.log("🔍 Pixel Tracking Health Report:", health);
    console.log("📊 Recent Events:", pixelLogger.getEvents().slice(0, 10));
    console.log("❌ Recent Errors:", pixelLogger.getRecentErrors());
    return {
      health,
      events: pixelLogger.getEvents(),
      exportLogs: () => pixelLogger.exportLogs(),
      clearLogs: () => pixelLogger.clearLogs(),
    };
  };
}
