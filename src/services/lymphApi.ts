// API service for Lymph-specific endpoints

/**
 * Submit quiz answers to get personalized recommendations
 */
export const submitQuizAnswers = async (data: {
  answers: Record<string, string[]>;
  email: string;
  firstName?: string;
  lastName?: string;
}) => {
  try {
    const response = await fetch('/api/lymph/submit-quiz', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });
    
    if (!response.ok) {
      throw new Error(`Error: ${response.status}`);
    }
    
    return await response.json();
  } catch (error) {
    console.error('Failed to submit quiz answers:', error);
    throw error;
  }
};

/**
 * Create a subscription for Lymph products
 */
export const createLymphSubscription = async (data: {
  planId: string;
  email: string;
  shippingDetails: any;
  paymentMethodId?: string;
}) => {
  try {
    const response = await fetch('/api/lymph/create-subscription', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });
    
    if (!response.ok) {
      throw new Error(`Error: ${response.status}`);
    }
    
    return await response.json();
  } catch (error) {
    console.error('Failed to create subscription:', error);
    throw error;
  }
};

/**
 * Create a one-time payment for Lymph products
 */
export const createLymphPayment = async (data: {
  planId: string;
  email: string;
  shippingDetails: any;
  paymentMethodId?: string;
}) => {
  try {
    const response = await fetch('/api/lymph/create-payment', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });
    
    if (!response.ok) {
      throw new Error(`Error: ${response.status}`);
    }
    
    return await response.json();
  } catch (error) {
    console.error('Failed to create payment:', error);
    throw error;
  }
};